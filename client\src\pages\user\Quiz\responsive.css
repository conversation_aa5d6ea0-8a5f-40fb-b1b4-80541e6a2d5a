/* ===== RESPONSIVE QUIZ SYSTEM ===== */

/* ===== BASE QUIZ CONTAINER ===== */
.quiz-container {
    min-height: 100vh;
    background: #ffffff;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
    position: relative;
    overflow-x: hidden;
}

/* ===== QUIZ PROGRESS BAR ===== */
.quiz-progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: #e5e7eb;
    z-index: 1000;
}

.quiz-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007BFF 0%, #0056D2 100%);
    transition: width 0.3s ease;
}

/* ===== QUIZ HEADER ===== */
.quiz-progress-container {
    padding: 1rem 1.5rem;
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 100;
}

.quiz-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.quiz-title-section h1 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.25rem 0;
    line-height: 1.4;
}

.quiz-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
}

/* ===== QUIZ TIMER ===== */
.quiz-timer {
    background: #f3f4f6;
    color: #374151;
    padding: 0.75rem 1rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
    min-width: 80px;
    text-align: center;
}

.quiz-timer.warning {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fca5a5;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* ===== QUIZ QUESTION COUNTER ===== */
.quiz-question-counter {
    text-align: center;
    font-size: 0.875rem;
    font-weight: 600;
    color: #007BFF;
    background: rgba(0, 123, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    display: inline-block;
    margin: 0 auto;
}

/* ===== QUIZ CONTENT AREA ===== */
.quiz-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1.5rem;
}

.quiz-question-container {
    background: #ffffff;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.quiz-question-number {
    font-size: 0.875rem;
    font-weight: 600;
    color: #007BFF;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.quiz-question-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.6;
    margin-bottom: 2rem;
}

/* ===== QUIZ OPTIONS ===== */
.quiz-options {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.quiz-option {
    background: #f9fafb;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1rem 1.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    font-weight: 500;
    color: #374151;
    position: relative;
    display: flex;
    align-items: center;
    min-height: 60px;
}

.quiz-option:hover {
    border-color: #007BFF;
    background: rgba(0, 123, 255, 0.05);
    transform: translateY(-1px);
}

.quiz-option.selected {
    border-color: #007BFF;
    background: #007BFF;
    color: #ffffff;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);
}

.quiz-option-letter {
    background: #e5e7eb;
    color: #374151;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    margin-right: 1rem;
    flex-shrink: 0;
    transition: all 0.2s ease;
}

.quiz-option.selected .quiz-option-letter {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
}

.quiz-option-text {
    flex: 1;
    line-height: 1.5;
}

/* ===== FILL IN THE BLANK ===== */
.quiz-fill-input {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    font-size: 1rem;
    background: #f9fafb;
    color: #374151;
    transition: all 0.2s ease;
    font-family: inherit;
}

.quiz-fill-input:focus {
    outline: none;
    border-color: #007BFF;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* ===== QUIZ NAVIGATION ===== */
.quiz-navigation {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    border-top: 1px solid #e5e7eb;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: center;
    gap: 1rem;
    z-index: 100;
}

.quiz-nav-btn {
    padding: 0.875rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    font-family: inherit;
}

.quiz-nav-btn.primary {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: #ffffff;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);
}

.quiz-nav-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
}

.quiz-nav-btn.secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.quiz-nav-btn.secondary:hover {
    background: #e5e7eb;
    color: #374151;
}

.quiz-nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* ===== QUIZ IMAGE DISPLAY ===== */
.quiz-image-container {
    text-align: center;
    margin: 1.5rem 0;
}

.quiz-image {
    max-width: 100%;
    max-height: 400px;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
    .quiz-progress-container {
        padding: 0.75rem 1rem;
    }

    .quiz-header-content {
        flex-direction: column;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .quiz-title-section {
        text-align: center;
        width: 100%;
    }

    .quiz-title-section h1 {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .quiz-subtitle {
        font-size: 0.8rem;
    }

    .quiz-timer {
        padding: 0.625rem 0.875rem;
        font-size: 0.875rem;
        min-width: 70px;
    }

    .quiz-question-counter {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }

    .quiz-content {
        padding: 1rem;
    }

    .quiz-question-container {
        padding: 1.25rem;
        margin-bottom: 1.5rem;
        border-radius: 0.75rem;
    }

    .quiz-question-text {
        font-size: 1.125rem;
        margin-bottom: 1.5rem;
        line-height: 1.5;
    }

    .quiz-options {
        gap: 0.75rem;
        margin-bottom: 1.5rem;
    }

    .quiz-option {
        padding: 0.875rem 1rem;
        min-height: 56px;
        font-size: 0.9375rem;
    }

    .quiz-option-letter {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
        margin-right: 0.75rem;
    }

    .quiz-fill-input {
        padding: 0.875rem;
        font-size: 1rem;
    }

    .quiz-navigation {
        padding: 0.875rem 1rem;
        gap: 0.75rem;
        flex-direction: column;
    }

    .quiz-nav-btn {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        min-width: 100%;
        border-radius: 0.5rem;
    }

    .quiz-image {
        max-height: 250px;
    }

    /* Hide left navigation during quiz */
    .layout-sidebar {
        display: none !important;
    }

    .layout-content {
        margin-left: 0 !important;
    }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .quiz-progress-container {
        padding: 1rem 1.25rem;
    }

    .quiz-header-content {
        margin-bottom: 1rem;
    }

    .quiz-title-section h1 {
        font-size: 1.125rem;
    }

    .quiz-timer {
        padding: 0.75rem 1rem;
        font-size: 0.9375rem;
    }

    .quiz-content {
        padding: 1.25rem;
    }

    .quiz-question-container {
        padding: 1.75rem;
        margin-bottom: 1.75rem;
    }

    .quiz-question-text {
        font-size: 1.1875rem;
        margin-bottom: 1.75rem;
    }

    .quiz-options {
        gap: 0.875rem;
        margin-bottom: 1.75rem;
    }

    .quiz-option {
        padding: 1rem 1.125rem;
        min-height: 58px;
        font-size: 0.9375rem;
    }

    .quiz-option-letter {
        width: 30px;
        height: 30px;
        margin-right: 0.875rem;
    }

    .quiz-navigation {
        padding: 1rem 1.25rem;
        gap: 0.875rem;
    }

    .quiz-nav-btn {
        padding: 0.9375rem 1.75rem;
        font-size: 0.9375rem;
        min-width: 140px;
    }

    .quiz-image {
        max-height: 320px;
    }
}

/* Desktop Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .quiz-content {
        padding: 1.5rem;
    }

    .quiz-question-container {
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .quiz-navigation {
        padding: 1rem 1.5rem;
    }

    .quiz-nav-btn {
        min-width: 140px;
    }
}

/* Large Desktop (1025px+) */
@media (min-width: 1025px) {
    .quiz-content {
        padding: 2rem;
    }

    .quiz-question-container {
        padding: 2.5rem;
        margin-bottom: 2.5rem;
    }

    .quiz-question-text {
        font-size: 1.375rem;
        margin-bottom: 2.5rem;
    }

    .quiz-options {
        gap: 1.25rem;
        margin-bottom: 2.5rem;
    }

    .quiz-option {
        padding: 1.25rem 1.5rem;
        min-height: 64px;
        font-size: 1.0625rem;
    }

    .quiz-option-letter {
        width: 36px;
        height: 36px;
        font-size: 0.9375rem;
        margin-right: 1.25rem;
    }

    .quiz-nav-btn {
        min-width: 160px;
        padding: 1rem 2.5rem;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .quiz-progress-container {
        padding: 0.5rem 1rem;
    }

    .quiz-header-content {
        margin-bottom: 0.5rem;
    }

    .quiz-title-section h1 {
        font-size: 1rem;
        margin-bottom: 0.125rem;
    }

    .quiz-subtitle {
        font-size: 0.75rem;
    }

    .quiz-question-counter {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .quiz-content {
        padding: 0.75rem;
    }

    .quiz-question-container {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .quiz-question-text {
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    .quiz-options {
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .quiz-option {
        padding: 0.75rem;
        min-height: 48px;
        font-size: 0.875rem;
    }

    .quiz-option-letter {
        width: 24px;
        height: 24px;
        font-size: 0.75rem;
        margin-right: 0.5rem;
    }

    .quiz-navigation {
        padding: 0.5rem 1rem;
        gap: 0.5rem;
    }

    .quiz-nav-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
        min-width: 100px;
    }

    .quiz-image {
        max-height: 200px;
    }
}

/* ===== QUIZ LISTING PAGE STYLES ===== */

.quiz-listing-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
}

.quiz-listing-content {
    max-width: 1400px;
    margin: 0 auto;
}

/* Quiz Header */
.quiz-listing-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.quiz-listing-title {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.quiz-listing-subtitle {
    font-size: 1.25rem;
    color: #6b7280;
    margin: 0;
    line-height: 1.5;
}

/* Search and Filter Section */
.quiz-search-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.quiz-search-grid {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    align-items: center;
}

.quiz-search-input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 2.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    font-size: 1rem;
    background: #f9fafb;
    color: #374151;
    transition: all 0.2s ease;
    position: relative;
}

.quiz-search-input:focus {
    outline: none;
    border-color: #007BFF;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.quiz-filter-select {
    min-width: 200px;
    padding: 0.875rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    font-size: 1rem;
    background: #f9fafb;
    color: #374151;
    transition: all 0.2s ease;
}

.quiz-filter-select:focus {
    outline: none;
    border-color: #007BFF;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Quiz Grid */
.quiz-grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.quiz-card-wrapper {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.quiz-card-wrapper:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    border-color: #007BFF;
}

/* Stats Cards */
.quiz-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.quiz-stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.quiz-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.quiz-stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: #007BFF;
    margin-bottom: 0.5rem;
}

.quiz-stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== QUIZ LISTING RESPONSIVE BREAKPOINTS ===== */

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
    .quiz-listing-container {
        padding: 1rem;
    }

    .quiz-listing-header {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-radius: 1rem;
    }

    .quiz-listing-title {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }

    .quiz-listing-subtitle {
        font-size: 1rem;
    }

    .quiz-search-section {
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-radius: 0.75rem;
    }

    .quiz-search-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .quiz-search-input {
        padding: 1rem;
        font-size: 1rem;
    }

    .quiz-filter-select {
        min-width: 100%;
        padding: 1rem;
        font-size: 1rem;
    }

    .quiz-grid-container {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .quiz-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
        margin-bottom: 1.5rem;
    }

    .quiz-stat-card {
        padding: 1rem;
    }

    .quiz-stat-number {
        font-size: 1.5rem;
    }

    .quiz-stat-label {
        font-size: 0.75rem;
    }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .quiz-listing-container {
        padding: 1.25rem;
    }

    .quiz-listing-header {
        padding: 1.75rem;
    }

    .quiz-listing-title {
        font-size: 2rem;
    }

    .quiz-listing-subtitle {
        font-size: 1.125rem;
    }

    .quiz-search-grid {
        grid-template-columns: 1fr auto;
        gap: 1rem;
    }

    .quiz-grid-container {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.25rem;
    }

    .quiz-stats-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }
}

/* Desktop Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .quiz-grid-container {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 1.5rem;
    }

    .quiz-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Large Desktop (1025px+) */
@media (min-width: 1025px) {
    .quiz-listing-container {
        padding: 2rem;
    }

    .quiz-listing-header {
        padding: 2.5rem;
    }

    .quiz-listing-title {
        font-size: 3rem;
    }

    .quiz-listing-subtitle {
        font-size: 1.375rem;
    }

    .quiz-search-section {
        padding: 2rem;
    }

    .quiz-grid-container {
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
        gap: 2rem;
    }

    .quiz-stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
    }

    .quiz-stat-card {
        padding: 2rem;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .quiz-listing-header {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .quiz-listing-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .quiz-listing-subtitle {
        font-size: 0.875rem;
    }

    .quiz-search-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .quiz-grid-container {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .quiz-stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .quiz-stat-card {
        padding: 0.75rem;
    }

    .quiz-stat-number {
        font-size: 1.25rem;
    }

    .quiz-stat-label {
        font-size: 0.7rem;
    }
}

/* ===== QUIZ START PAGE STYLES ===== */

.quiz-start-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.5rem;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
}

.quiz-start-card {
    width: 100%;
    max-width: 600px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.quiz-start-header {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.quiz-start-title {
    font-size: 1.875rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.quiz-start-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

.quiz-start-content {
    padding: 2rem;
}

.quiz-start-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.quiz-info-item {
    background: #f8fafc;
    border-radius: 0.75rem;
    padding: 1rem;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.quiz-info-icon {
    font-size: 1.5rem;
    color: #007BFF;
    margin-bottom: 0.5rem;
}

.quiz-info-value {
    font-size: 1.125rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.quiz-info-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.quiz-start-instructions {
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 2rem;
}

.quiz-start-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.quiz-start-btn {
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    min-width: 140px;
}

.quiz-start-btn.primary {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);
}

.quiz-start-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
}

.quiz-start-btn.secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.quiz-start-btn.secondary:hover {
    background: #e5e7eb;
    color: #374151;
}

/* ===== QUIZ RESULT PAGE STYLES ===== */

.quiz-result-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
}

.quiz-result-content {
    max-width: 1000px;
    margin: 0 auto;
}

.quiz-result-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    margin-bottom: 2rem;
}

.quiz-result-header {
    padding: 3rem 2rem;
    text-align: center;
}

.quiz-result-header.pass {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.quiz-result-header.fail {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.quiz-result-gif {
    width: 120px;
    height: 120px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    object-fit: cover;
}

.quiz-result-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.quiz-result-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin: 0;
}

.quiz-result-stats {
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.quiz-result-stat {
    text-align: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 1rem;
    border: 1px solid #e2e8f0;
}

.quiz-result-stat-value {
    font-size: 2rem;
    font-weight: 800;
    color: #007BFF;
    margin-bottom: 0.5rem;
}

.quiz-result-stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.quiz-result-progress {
    margin: 2rem 0;
    text-align: center;
}

.quiz-result-progress-bar {
    width: 100%;
    height: 1.5rem;
    background: #e5e7eb;
    border-radius: 0.75rem;
    overflow: hidden;
    margin: 1rem 0;
}

.quiz-result-progress-fill {
    height: 100%;
    border-radius: 0.75rem;
    transition: width 1s ease;
}

.quiz-result-progress-fill.pass {
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.quiz-result-progress-fill.fail {
    background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
}

.quiz-result-percentage {
    font-size: 2rem;
    font-weight: 800;
    color: #1f2937;
    margin-top: 0.75rem;
}

.quiz-result-actions {
    padding: 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    border-top: 1px solid #e5e7eb;
}

.quiz-result-btn {
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    min-width: 140px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.quiz-result-btn.primary {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);
}

.quiz-result-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
    text-decoration: none;
    color: white;
}

.quiz-result-btn.secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.quiz-result-btn.secondary:hover {
    background: #e5e7eb;
    color: #374151;
    text-decoration: none;
}

/* Question Review Section */
.quiz-review-section {
    margin-top: 2rem;
}

.quiz-review-question {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.quiz-review-question-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.quiz-review-question-header.correct {
    background: #f0fdf4;
    border-bottom-color: #bbf7d0;
}

.quiz-review-question-header.incorrect {
    background: #fef2f2;
    border-bottom-color: #fecaca;
}

.quiz-review-question-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.quiz-review-question-status {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.quiz-review-question-status.correct {
    background: #dcfce7;
    color: #166534;
}

.quiz-review-question-status.incorrect {
    background: #fee2e2;
    color: #991b1b;
}

.quiz-review-question-content {
    padding: 1.5rem;
}

.quiz-review-question-text {
    font-size: 1rem;
    color: #374151;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.quiz-review-answers {
    display: grid;
    gap: 0.75rem;
}

.quiz-review-answer {
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.quiz-review-answer.correct {
    background: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
}

.quiz-review-answer.incorrect {
    background: #fef2f2;
    border-color: #fecaca;
    color: #991b1b;
}

.quiz-review-answer.user-selected {
    font-weight: 600;
}

.quiz-review-answer-icon {
    font-size: 1rem;
    font-weight: 700;
}

.quiz-review-answer-text {
    flex: 1;
}

.quiz-explanation-btn {
    margin-top: 1rem;
    padding: 0.75rem 1.5rem;
    background: #007BFF;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quiz-explanation-btn:hover {
    background: #0056D2;
    transform: translateY(-1px);
}

/* ===== QUIZ START & RESULT RESPONSIVE BREAKPOINTS ===== */

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
    .quiz-start-container {
        padding: 1rem;
        align-items: flex-start;
        padding-top: 2rem;
    }

    .quiz-start-card {
        max-width: 100%;
        border-radius: 1rem;
    }

    .quiz-start-header {
        padding: 1.5rem;
    }

    .quiz-start-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .quiz-start-subtitle {
        font-size: 0.875rem;
    }

    .quiz-start-content {
        padding: 1.5rem;
    }

    .quiz-start-info {
        grid-template-columns: 1fr;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
    }

    .quiz-info-item {
        padding: 0.875rem;
    }

    .quiz-info-icon {
        font-size: 1.25rem;
    }

    .quiz-info-value {
        font-size: 1rem;
    }

    .quiz-info-label {
        font-size: 0.8rem;
    }

    .quiz-start-instructions {
        padding: 0.875rem;
        margin-bottom: 1.5rem;
        font-size: 0.875rem;
    }

    .quiz-start-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .quiz-start-btn {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        min-width: 100%;
    }

    /* Quiz Result Mobile */
    .quiz-result-container {
        padding: 1rem;
    }

    .quiz-result-header {
        padding: 2rem 1.5rem;
    }

    .quiz-result-gif {
        width: 80px;
        height: 80px;
        margin-bottom: 1rem;
    }

    .quiz-result-title {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }

    .quiz-result-subtitle {
        font-size: 1rem;
    }

    .quiz-result-stats {
        padding: 1.5rem;
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .quiz-result-stat {
        padding: 1rem;
    }

    .quiz-result-stat-value {
        font-size: 1.5rem;
    }

    .quiz-result-stat-label {
        font-size: 0.8rem;
    }

    .quiz-result-progress {
        margin: 1.5rem 0;
    }

    .quiz-result-progress-bar {
        height: 1.25rem;
    }

    .quiz-result-percentage {
        font-size: 1.75rem;
    }

    .quiz-result-actions {
        padding: 1.5rem;
        flex-direction: column;
        gap: 0.75rem;
    }

    .quiz-result-btn {
        padding: 1rem 1.5rem;
        min-width: 100%;
    }

    /* Quiz Review Mobile */
    .quiz-review-question {
        margin-bottom: 1rem;
        border-radius: 0.75rem;
    }

    .quiz-review-question-header {
        padding: 1rem;
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .quiz-review-question-title {
        font-size: 1rem;
    }

    .quiz-review-question-status {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }

    .quiz-review-question-content {
        padding: 1rem;
    }

    .quiz-review-question-text {
        font-size: 0.9375rem;
        margin-bottom: 0.875rem;
    }

    .quiz-review-answers {
        gap: 0.5rem;
    }

    .quiz-review-answer {
        padding: 0.625rem 0.875rem;
        gap: 0.5rem;
    }

    .quiz-review-answer-icon {
        font-size: 0.875rem;
    }

    .quiz-explanation-btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .quiz-start-container {
        padding: 1.25rem;
    }

    .quiz-start-header {
        padding: 1.75rem;
    }

    .quiz-start-title {
        font-size: 1.75rem;
    }

    .quiz-start-content {
        padding: 1.75rem;
    }

    .quiz-start-info {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .quiz-start-actions {
        gap: 0.875rem;
    }

    .quiz-start-btn {
        min-width: 160px;
    }

    /* Quiz Result Tablet */
    .quiz-result-container {
        padding: 1.25rem;
    }

    .quiz-result-header {
        padding: 2.5rem 2rem;
    }

    .quiz-result-gif {
        width: 100px;
        height: 100px;
    }

    .quiz-result-title {
        font-size: 2rem;
    }

    .quiz-result-subtitle {
        font-size: 1.125rem;
    }

    .quiz-result-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.25rem;
    }

    .quiz-result-actions {
        gap: 0.875rem;
    }

    .quiz-result-btn {
        min-width: 160px;
    }

    /* Quiz Review Tablet */
    .quiz-review-question-header {
        padding: 1.25rem;
    }

    .quiz-review-question-content {
        padding: 1.25rem;
    }
}

/* Desktop Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .quiz-start-info {
        grid-template-columns: repeat(3, 1fr);
    }

    .quiz-result-stats {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Large Desktop (1025px+) */
@media (min-width: 1025px) {
    .quiz-start-container {
        padding: 2rem;
    }

    .quiz-start-card {
        max-width: 700px;
    }

    .quiz-start-header {
        padding: 2.5rem;
    }

    .quiz-start-title {
        font-size: 2.25rem;
    }

    .quiz-start-subtitle {
        font-size: 1.125rem;
    }

    .quiz-start-content {
        padding: 2.5rem;
    }

    .quiz-start-info {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
        margin-bottom: 2.5rem;
    }

    .quiz-info-item {
        padding: 1.25rem;
    }

    .quiz-start-btn {
        padding: 1.125rem 2.5rem;
        font-size: 1.0625rem;
        min-width: 180px;
    }

    /* Quiz Result Large Desktop */
    .quiz-result-container {
        padding: 2rem;
    }

    .quiz-result-content {
        max-width: 1200px;
    }

    .quiz-result-header {
        padding: 3.5rem 2.5rem;
    }

    .quiz-result-gif {
        width: 140px;
        height: 140px;
        margin-bottom: 2rem;
    }

    .quiz-result-title {
        font-size: 3rem;
        margin-bottom: 1.25rem;
    }

    .quiz-result-subtitle {
        font-size: 1.375rem;
    }

    .quiz-result-stats {
        padding: 2.5rem;
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
    }

    .quiz-result-stat {
        padding: 2rem;
    }

    .quiz-result-stat-value {
        font-size: 2.5rem;
    }

    .quiz-result-actions {
        padding: 2.5rem;
        gap: 1.5rem;
    }

    .quiz-result-btn {
        padding: 1.125rem 2.5rem;
        font-size: 1.0625rem;
        min-width: 180px;
    }

    /* Quiz Review Large Desktop */
    .quiz-review-question {
        margin-bottom: 2rem;
    }

    .quiz-review-question-header {
        padding: 2rem;
    }

    .quiz-review-question-title {
        font-size: 1.25rem;
    }

    .quiz-review-question-content {
        padding: 2rem;
    }

    .quiz-review-question-text {
        font-size: 1.0625rem;
        margin-bottom: 1.25rem;
    }

    .quiz-review-answers {
        gap: 1rem;
    }

    .quiz-review-answer {
        padding: 1rem 1.25rem;
        gap: 1rem;
    }

    .quiz-explanation-btn {
        padding: 0.875rem 1.75rem;
        font-size: 1rem;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .quiz-start-container {
        padding: 0.75rem;
        align-items: center;
    }

    .quiz-start-header {
        padding: 1rem;
    }

    .quiz-start-title {
        font-size: 1.25rem;
        margin-bottom: 0.25rem;
    }

    .quiz-start-subtitle {
        font-size: 0.8rem;
    }

    .quiz-start-content {
        padding: 1rem;
    }

    .quiz-start-info {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .quiz-info-item {
        padding: 0.75rem;
    }

    .quiz-info-icon {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .quiz-info-value {
        font-size: 0.875rem;
        margin-bottom: 0.125rem;
    }

    .quiz-info-label {
        font-size: 0.7rem;
    }

    .quiz-start-instructions {
        padding: 0.75rem;
        margin-bottom: 1rem;
        font-size: 0.8rem;
    }

    .quiz-start-actions {
        gap: 0.5rem;
    }

    .quiz-start-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
        min-width: 120px;
    }

    /* Quiz Result Landscape Mobile */
    .quiz-result-container {
        padding: 0.75rem;
    }

    .quiz-result-header {
        padding: 1.5rem 1rem;
    }

    .quiz-result-gif {
        width: 60px;
        height: 60px;
        margin-bottom: 0.75rem;
    }

    .quiz-result-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .quiz-result-subtitle {
        font-size: 0.875rem;
    }

    .quiz-result-stats {
        padding: 1rem;
        grid-template-columns: repeat(4, 1fr);
        gap: 0.75rem;
    }

    .quiz-result-stat {
        padding: 0.75rem;
    }

    .quiz-result-stat-value {
        font-size: 1.25rem;
    }

    .quiz-result-stat-label {
        font-size: 0.7rem;
    }

    .quiz-result-progress {
        margin: 1rem 0;
    }

    .quiz-result-progress-bar {
        height: 1rem;
    }

    .quiz-result-percentage {
        font-size: 1.5rem;
        margin-top: 0.5rem;
    }

    .quiz-result-actions {
        padding: 1rem;
        gap: 0.5rem;
    }

    .quiz-result-btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
        min-width: 120px;
    }
}

/* ===== UTILITY CLASSES FOR QUIZ SYSTEM ===== */

/* Hide elements on mobile */
@media (max-width: 768px) {
    .hide-on-mobile {
        display: none !important;
    }

    /* Full screen quiz experience on mobile */
    .quiz-fullscreen-mobile {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999;
        background: white;
    }

    /* Hide sidebar during quiz on mobile */
    .layout-sidebar {
        display: none !important;
    }

    .layout-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }
}

/* Show elements only on mobile */
@media (min-width: 769px) {
    .show-on-mobile-only {
        display: none !important;
    }
}

/* Accessibility improvements */
.quiz-option:focus,
.quiz-nav-btn:focus,
.quiz-start-btn:focus,
.quiz-result-btn:focus {
    outline: 2px solid #007BFF;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .quiz-option {
        border-width: 3px;
    }

    .quiz-option.selected {
        border-width: 4px;
    }

    .quiz-timer.warning {
        border-width: 3px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .quiz-option,
    .quiz-nav-btn,
    .quiz-start-btn,
    .quiz-result-btn,
    .quiz-card-wrapper {
        transition: none !important;
        animation: none !important;
    }

    .quiz-timer.warning {
        animation: none !important;
    }
}

/* Print styles */
@media print {
    .quiz-navigation,
    .quiz-timer,
    .quiz-progress-bar,
    .quiz-start-actions,
    .quiz-result-actions {
        display: none !important;
    }

    .quiz-container,
    .quiz-start-container,
    .quiz-result-container {
        background: white !important;
        box-shadow: none !important;
    }

    .quiz-question-container,
    .quiz-start-card,
    .quiz-result-card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
}

/* Dark mode support (if implemented) */
@media (prefers-color-scheme: dark) {
    .quiz-container,
    .quiz-listing-container,
    .quiz-start-container,
    .quiz-result-container {
        background: #1f2937;
        color: #f9fafb;
    }

    .quiz-question-container,
    .quiz-start-card,
    .quiz-result-card,
    .quiz-card-wrapper {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }

    .quiz-option {
        background: #4b5563;
        border-color: #6b7280;
        color: #f9fafb;
    }

    .quiz-option:hover {
        background: #374151;
        border-color: #007BFF;
    }

    .quiz-fill-input {
        background: #4b5563;
        border-color: #6b7280;
        color: #f9fafb;
    }

    .quiz-timer {
        background: #4b5563;
        color: #f9fafb;
        border-color: #6b7280;
    }
}

/* Very small screens (smartwatches, etc.) */
@media (max-width: 280px) {
    .quiz-progress-container {
        padding: 0.5rem;
    }

    .quiz-title-section h1 {
        font-size: 0.875rem;
    }

    .quiz-subtitle {
        font-size: 0.7rem;
    }

    .quiz-timer {
        padding: 0.5rem;
        font-size: 0.8rem;
        min-width: 60px;
    }

    .quiz-content {
        padding: 0.5rem;
    }

    .quiz-question-container {
        padding: 0.75rem;
    }

    .quiz-question-text {
        font-size: 0.875rem;
    }

    .quiz-option {
        padding: 0.5rem;
        min-height: 40px;
        font-size: 0.8rem;
    }

    .quiz-option-letter {
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
        margin-right: 0.375rem;
    }

    .quiz-nav-btn {
        padding: 0.625rem 1rem;
        font-size: 0.8rem;
        min-width: 80px;
    }
}

/* Large screens (4K, ultrawide) */
@media (min-width: 1920px) {
    .quiz-listing-content,
    .quiz-result-content {
        max-width: 1600px;
    }

    .quiz-question-container {
        max-width: 1000px;
        margin-left: auto;
        margin-right: auto;
    }

    .quiz-question-text {
        font-size: 1.5rem;
    }

    .quiz-option {
        font-size: 1.125rem;
        min-height: 70px;
    }

    .quiz-option-letter {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}
