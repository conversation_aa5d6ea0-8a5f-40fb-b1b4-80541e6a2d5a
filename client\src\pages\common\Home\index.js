import React, { useState, useEffect, useRef } from "react";
import "./index.css";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import {
  TbArrowBigRightLinesFilled,
  TbBrain,
  TbBook,
  TbTrophy,
  TbUsers,
  TbStar,
  TbSchool,
  TbMenu2,
  TbX,
  TbMoon,
  TbSun
} from "react-icons/tb";
import { Rate, message } from "antd";
import { useDispatch } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { getAllReviews } from "../../../apicalls/reviews";
import Image1 from "../../../assets/collage-1.png";
import Image2 from "../../../assets/collage-2.png";
import { contactUs } from "../../../apicalls/users";
import { useTheme } from "../../../contexts/ThemeContext";
import { Button } from "../../../components/modern";

const Home = () => {
  const homeSectionRef = useRef(null);
  const aboutUsSectionRef = useRef(null);
  const reviewsSectionRef = useRef(null);
  const contactUsRef = useRef(null);
  const [reviews, setReviews] = useState([]);
  const dispatch = useDispatch();
  const [menuOpen, setMenuOpen] = useState(false);
  const [formData, setFormData] = useState({ name: "", email: "", message: "" });
  const [loading, setLoading] = useState(false);
  const [responseMessage, setResponseMessage] = useState("");
  const { isDarkMode, toggleTheme } = useTheme();

  useEffect(() => { getReviews(); }, []);

  const getReviews = async () => {
    dispatch(ShowLoading());
    try {
      const response = await getAllReviews();
      if (response.success) {
        setReviews(response.data);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    }
    dispatch(HideLoading());
  };

  const scrollToSection = (ref, offset = 30) => {
    if (ref?.current) {
      const sectionTop = ref.current.offsetTop;
      window.scrollTo({ top: sectionTop - offset, behavior: "smooth" });
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setResponseMessage("");
    try {
      const data = await contactUs(formData);
      if (data.success) {
        message.success("Message sent successfully!");
        setResponseMessage("Message sent successfully!");
        setFormData({ name: "", email: "", message: "" });
      } else {
        setResponseMessage(data.message || "Something went wrong.");
      }
    } catch (error) {
      setResponseMessage("Error sending message. Please try again.");
    }
    setLoading(false);
  };

  return (
    <div className="Home">
      {/* Navigation */}
      <motion.nav
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="nav-header fixed w-full top-0 z-50"
      >
        <div className="container">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-2">
              <TbBrain className="w-8 h-8" style={{color: '#007BFF'}} />
              <span className="logo-text">
                Brain<span className="logo-accent">Wave</span>
              </span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <button onClick={() => scrollToSection(homeSectionRef)} className="nav-item">Home</button>
              <button onClick={() => scrollToSection(aboutUsSectionRef)} className="nav-item">About Us</button>
              <button onClick={() => scrollToSection(reviewsSectionRef)} className="nav-item">Reviews</button>
              <button onClick={() => scrollToSection(contactUsRef)} className="nav-item">Contact Us</button>
            </div>

            {/* Right Section */}
            <div className="flex items-center space-x-4">
              {/* Theme Toggle */}
              <button
                onClick={toggleTheme}
                className="p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
              >
                {isDarkMode ? <TbSun className="w-5 h-5" /> : <TbMoon className="w-5 h-5" />}
              </button>

              {/* Auth Buttons - Desktop */}
              <div className="hidden md:flex items-center space-x-3">
                <Link to="/login">
                  <button className="btn btn-secondary">Login</button>
                </Link>
                <Link to="/register">
                  <button className="btn btn-primary">Sign Up</button>
                </Link>
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setMenuOpen(!menuOpen)}
                className="md:hidden p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
              >
                {menuOpen ? <TbX className="w-6 h-6" /> : <TbMenu2 className="w-6 h-6" />}
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {menuOpen && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="md:hidden mobile-nav"
            >
              <div className="flex flex-col">
                <button onClick={() => { scrollToSection(homeSectionRef); setMenuOpen(false); }} className="nav-item">Home</button>
                <button onClick={() => { scrollToSection(aboutUsSectionRef); setMenuOpen(false); }} className="nav-item">About Us</button>
                <button onClick={() => { scrollToSection(reviewsSectionRef); setMenuOpen(false); }} className="nav-item">Reviews</button>
                <button onClick={() => { scrollToSection(contactUsRef); setMenuOpen(false); }} className="nav-item">Contact Us</button>
                <div className="flex flex-col gap-3 pt-4 border-t border-gray-200 mt-4">
                  <Link to="/login">
                    <button className="btn btn-secondary w-full">Login</button>
                  </Link>
                  <Link to="/register">
                    <button className="btn btn-primary w-full">Sign Up</button>
                  </Link>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </motion.nav>

      {/* Hero Section */}
      <section ref={homeSectionRef} className="hero-section">
        <div className="container">
          <div className="hero-grid">
            {/* Hero Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="hero-content"
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="hero-badge"
              >
                <TbSchool className="w-5 h-5 mr-2" />
                #1 Educational Platform in Tanzania
              </motion.div>

              <h1 className="hero-title">
                Fueling Bright Futures with{" "}
                <span className="text-gradient">
                  Education
                  <TbArrowBigRightLinesFilled className="inline w-8 h-8 ml-2" />
                </span>
              </h1>

              <p className="hero-subtitle">
                Discover limitless learning opportunities with our comprehensive
                online study platform. Study anywhere, anytime, and achieve your
                academic goals with confidence.
              </p>

              {/* CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="cta-buttons"
              >
                <Link to="/register">
                  <button className="btn btn-primary">
                    Get Started Free
                  </button>
                </Link>
                <Link to="/login">
                  <button className="btn btn-secondary">
                    Sign In
                  </button>
                </Link>
              </motion.div>

              {/* Trust Indicators */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="trust-indicators"
              >
                <div className="trust-indicator">
                  <TbUsers style={{color: '#007BFF'}} />
                  <span>15K+ Students</span>
                </div>
                <div className="trust-indicator">
                  <TbStar style={{color: '#f59e0b'}} />
                  <span>4.9/5 Rating</span>
                </div>
                <div className="trust-indicator">
                  <TbTrophy style={{color: '#007BFF'}} />
                  <span>Award Winning</span>
                </div>
              </motion.div>
            </motion.div>

            {/* Hero Image */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="hero-image"
            >
              <div className="relative">
                <img
                  src={Image1}
                  alt="Students Learning"
                />

                {/* Floating Elements */}
                <motion.div
                  animate={{ y: [-10, 10, -10] }}
                  transition={{ duration: 4, repeat: Infinity }}
                  className="floating-element"
                  style={{top: '-1rem', left: '-1rem'}}
                >
                  <TbBook style={{color: '#007BFF'}} />
                </motion.div>

                <motion.div
                  animate={{ y: [10, -10, 10] }}
                  transition={{ duration: 3, repeat: Infinity }}
                  className="floating-element"
                  style={{bottom: '-1rem', right: '-1rem'}}
                >
                  <TbTrophy style={{color: '#f59e0b'}} />
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
