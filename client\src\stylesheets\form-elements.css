/* Modern form elements - enhanced for better UX */
input {
  height: auto;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  width: 100%;
  font-size: var(--font-size-sm);
  transition: var(--transition-normal);
  background-color: var(--white);
  color: var(--gray-700);
  font-family: inherit;
}

input:focus {
  outline: none;
  border-color: var(--primary);
  background-color: var(--white);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

input:hover:not(:focus) {
  border-color: var(--gray-300);
}

input:disabled {
  background-color: var(--gray-50);
  color: var(--gray-500);
  cursor: not-allowed;
  opacity: 0.7;
}

input::placeholder {
  color: var(--gray-400);
  font-weight: 400;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

.ant-form-item {
  margin-bottom: 5px !important;
}

/* Modern button styles */
button {
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  height: auto !important;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition-normal);
  border: none;
  font-size: var(--font-size-sm);
  font-family: inherit;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  position: relative;
  overflow: hidden;
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.primary-contained-btn {
  background: var(--gradient-primary);
  color: var(--white);
  box-shadow: var(--shadow-primary);
}

.primary-contained-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px -5px rgba(0, 123, 255, 0.3);
}

.primary-contained-btn:active {
  transform: translateY(0);
}

.primary-contained-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.mobile-btn {
  padding: 0px 10px;
  font-size: small;
}

.primary-outlined-btn {
  background-color: white;
  color: var(--primary);
  border: 2px solid var(--primary);
}

select {
  height: auto;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  width: 100%;
  appearance: none;
  background-color: #f9fafb;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

select:focus {
  outline: none;
  border-color: #3b82f6;
  background-color: #ffffff;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
