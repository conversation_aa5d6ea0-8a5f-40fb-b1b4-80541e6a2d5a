[{"C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js": "4", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js": "5", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js": "6", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js": "7", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js": "8", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js": "9", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js": "10", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Announcement\\AnnouncementModal.jsx": "11", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Announcement\\Announcement.jsx": "12", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js": "13", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js": "14", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\Plans.jsx": "15", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js": "16", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js": "17", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js": "18", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js": "19", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js": "20", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js": "21", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js": "22", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js": "23", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js": "24", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js": "25", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js": "26", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js": "27", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js": "28", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js": "29", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Chat\\index.jsx": "30", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js": "31", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js": "32", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js": "33", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js": "34", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\announcements.js": "35", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js": "36", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js": "37", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js": "38", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js": "39", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx": "40", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js": "41", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js": "42", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js": "43", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js": "44", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js": "45", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js": "46", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\WaitingModal.jsx": "47", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\ConfirmModal.jsx": "48", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js": "49", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js": "50", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js": "51", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js": "52", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js": "53", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js": "54", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js": "55", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\index.js": "56", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js": "57", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionGenerationForm.js": "58", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionPreview.js": "59", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\AutoGenerateExamModal.js": "60", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\authUtils.js": "61", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AILoginModal.js": "62", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\hooks\\useAIAuth.js": "63", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\auth.js": "64", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js": "65", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizStart.js": "66", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js": "67", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\QuizRenderer.js": "68", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js": "69", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js": "70", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js": "71", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js": "72", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js": "73", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js": "74", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js": "75", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js": "76", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js": "77", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js": "78", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js": "79", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js": "80", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js": "81", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js": "82", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js": "83"}, {"size": 395, "mtime": 1696247250000, "results": "84", "hashOfConfig": "85"}, {"size": 7355, "mtime": 1751170514749, "results": "86", "hashOfConfig": "85"}, {"size": 362, "mtime": 1696247250000, "results": "87", "hashOfConfig": "85"}, {"size": 430, "mtime": 1736735017645, "results": "88", "hashOfConfig": "85"}, {"size": 180, "mtime": 1696247250000, "results": "89", "hashOfConfig": "85"}, {"size": 17622, "mtime": 1751164899839, "results": "90", "hashOfConfig": "85"}, {"size": 334, "mtime": 1696247250000, "results": "91", "hashOfConfig": "85"}, {"size": 416, "mtime": 1696247250000, "results": "92", "hashOfConfig": "85"}, {"size": 404, "mtime": 1736731932223, "results": "93", "hashOfConfig": "85"}, {"size": 449, "mtime": 1736732007232, "results": "94", "hashOfConfig": "85"}, {"size": 2226, "mtime": 1749936905425, "results": "95", "hashOfConfig": "85"}, {"size": 4816, "mtime": 1748816644854, "results": "96", "hashOfConfig": "85"}, {"size": 12926, "mtime": 1751134094183, "results": "97", "hashOfConfig": "85"}, {"size": 2884, "mtime": 1748816644857, "results": "98", "hashOfConfig": "85"}, {"size": 7653, "mtime": 1750329453221, "results": "99", "hashOfConfig": "85"}, {"size": 34186, "mtime": 1751165143072, "results": "100", "hashOfConfig": "85"}, {"size": 44125, "mtime": 1751168562187, "results": "101", "hashOfConfig": "85"}, {"size": 2243, "mtime": 1735567977494, "results": "102", "hashOfConfig": "85"}, {"size": 16558, "mtime": 1751165104089, "results": "103", "hashOfConfig": "85"}, {"size": 17309, "mtime": 1751143176534, "results": "104", "hashOfConfig": "85"}, {"size": 1327, "mtime": 1709427669270, "results": "105", "hashOfConfig": "85"}, {"size": 8089, "mtime": 1740446459586, "results": "106", "hashOfConfig": "85"}, {"size": 4251, "mtime": 1751165083094, "results": "107", "hashOfConfig": "85"}, {"size": 11349, "mtime": 1751142585774, "results": "108", "hashOfConfig": "85"}, {"size": 7506, "mtime": 1751175922655, "results": "109", "hashOfConfig": "85"}, {"size": 2894, "mtime": 1751175702044, "results": "110", "hashOfConfig": "85"}, {"size": 15635, "mtime": 1737822439601, "results": "111", "hashOfConfig": "85"}, {"size": 15613, "mtime": 1751103257226, "results": "112", "hashOfConfig": "85"}, {"size": 17410, "mtime": 1751174607034, "results": "113", "hashOfConfig": "85"}, {"size": 4579, "mtime": 1749938582942, "results": "114", "hashOfConfig": "85"}, {"size": 522, "mtime": 1736735708590, "results": "115", "hashOfConfig": "85"}, {"size": 2578, "mtime": 1740446459580, "results": "116", "hashOfConfig": "85"}, {"size": 2204, "mtime": 1696247250000, "results": "117", "hashOfConfig": "85"}, {"size": 388, "mtime": 1703845955779, "results": "118", "hashOfConfig": "85"}, {"size": 1095, "mtime": 1748816644845, "results": "119", "hashOfConfig": "85"}, {"size": 279, "mtime": 1736719733927, "results": "120", "hashOfConfig": "85"}, {"size": 1104, "mtime": 1749936905424, "results": "121", "hashOfConfig": "85"}, {"size": 1179, "mtime": 1703618041193, "results": "122", "hashOfConfig": "85"}, {"size": 5595, "mtime": 1751164672302, "results": "123", "hashOfConfig": "85"}, {"size": 944, "mtime": 1750970590507, "results": "124", "hashOfConfig": "85"}, {"size": 6669, "mtime": 1750999504134, "results": "125", "hashOfConfig": "85"}, {"size": 12864, "mtime": 1751134045332, "results": "126", "hashOfConfig": "85"}, {"size": 4091, "mtime": 1751125746303, "results": "127", "hashOfConfig": "85"}, {"size": 8101, "mtime": 1750963515173, "results": "128", "hashOfConfig": "85"}, {"size": 578, "mtime": 1705434185826, "results": "129", "hashOfConfig": "85"}, {"size": 1787, "mtime": 1734985908268, "results": "130", "hashOfConfig": "85"}, {"size": 2748, "mtime": 1736737718411, "results": "131", "hashOfConfig": "85"}, {"size": 2421, "mtime": 1737107445778, "results": "132", "hashOfConfig": "85"}, {"size": 3692, "mtime": 1751088963669, "results": "133", "hashOfConfig": "85"}, {"size": 8145, "mtime": 1751000372079, "results": "134", "hashOfConfig": "85"}, {"size": 29072, "mtime": 1750992761364, "results": "135", "hashOfConfig": "85"}, {"size": 9494, "mtime": 1750995979612, "results": "136", "hashOfConfig": "85"}, {"size": 1524, "mtime": 1750994293078, "results": "137", "hashOfConfig": "85"}, {"size": 17375, "mtime": 1751000106093, "results": "138", "hashOfConfig": "85"}, {"size": 11161, "mtime": 1750999560542, "results": "139", "hashOfConfig": "85"}, {"size": 8252, "mtime": 1751004143541, "results": "140", "hashOfConfig": "85"}, {"size": 3047, "mtime": 1751086581664, "results": "141", "hashOfConfig": "85"}, {"size": 25462, "mtime": 1751089065189, "results": "142", "hashOfConfig": "85"}, {"size": 10774, "mtime": 1751085763434, "results": "143", "hashOfConfig": "85"}, {"size": 11689, "mtime": 1751100954560, "results": "144", "hashOfConfig": "85"}, {"size": 3850, "mtime": 1751088011224, "results": "145", "hashOfConfig": "85"}, {"size": 5991, "mtime": 1751088070022, "results": "146", "hashOfConfig": "85"}, {"size": 5741, "mtime": 1751088101803, "results": "147", "hashOfConfig": "85"}, {"size": 3690, "mtime": 1751088038266, "results": "148", "hashOfConfig": "85"}, {"size": 13867, "mtime": 1751133136111, "results": "149", "hashOfConfig": "85"}, {"size": 9370, "mtime": 1751144335083, "results": "150", "hashOfConfig": "85"}, {"size": 6675, "mtime": 1751111148871, "results": "151", "hashOfConfig": "85"}, {"size": 5774, "mtime": 1751170149683, "results": "152", "hashOfConfig": "85"}, {"size": 5879, "mtime": 1751169872754, "results": "153", "hashOfConfig": "85"}, {"size": 1410, "mtime": 1751140352157, "results": "154", "hashOfConfig": "85"}, {"size": 1154, "mtime": 1751143356792, "results": "155", "hashOfConfig": "85"}, {"size": 2324, "mtime": 1751140401815, "results": "156", "hashOfConfig": "85"}, {"size": 2913, "mtime": 1751140370241, "results": "157", "hashOfConfig": "85"}, {"size": 1857, "mtime": 1751140385464, "results": "158", "hashOfConfig": "85"}, {"size": 3119, "mtime": 1751164996340, "results": "159", "hashOfConfig": "85"}, {"size": 6185, "mtime": 1751143766336, "results": "160", "hashOfConfig": "85"}, {"size": 7991, "mtime": 1751164628709, "results": "161", "hashOfConfig": "85"}, {"size": 6005, "mtime": 1751140813615, "results": "162", "hashOfConfig": "85"}, {"size": 2576, "mtime": 1751143230244, "results": "163", "hashOfConfig": "85"}, {"size": 3904, "mtime": 1751143777976, "results": "164", "hashOfConfig": "85"}, {"size": 5088, "mtime": 1751143254906, "results": "165", "hashOfConfig": "85"}, {"size": 4989, "mtime": 1751143312418, "results": "166", "hashOfConfig": "85"}, {"size": 6307, "mtime": 1751143343580, "results": "167", "hashOfConfig": "85"}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, "1ymk59w", {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "187", "usedDeprecatedRules": "171"}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "206", "usedDeprecatedRules": "171"}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "210", "usedDeprecatedRules": "171"}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "214", "usedDeprecatedRules": "171"}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "218", "usedDeprecatedRules": "171"}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "222", "usedDeprecatedRules": "171"}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "226", "usedDeprecatedRules": "171"}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "230", "usedDeprecatedRules": "171"}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "234", "usedDeprecatedRules": "171"}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "238", "usedDeprecatedRules": "171"}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "242", "usedDeprecatedRules": "171"}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "246", "usedDeprecatedRules": "171"}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "250", "usedDeprecatedRules": "171"}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "254", "usedDeprecatedRules": "171"}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "264", "usedDeprecatedRules": "171"}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "268", "usedDeprecatedRules": "171"}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "275", "usedDeprecatedRules": "171"}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "303", "usedDeprecatedRules": "171"}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "328", "usedDeprecatedRules": "171"}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "353", "usedDeprecatedRules": "171"}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "357", "usedDeprecatedRules": "171"}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "364", "usedDeprecatedRules": "171"}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "377", "usedDeprecatedRules": "171"}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "381", "usedDeprecatedRules": "171"}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "433", "usedDeprecatedRules": "171"}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "171"}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "440", "usedDeprecatedRules": "171"}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "444", "usedDeprecatedRules": "171"}, "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\index.js", [], [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Loader.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ProtectedRoute.js", ["445", "446", "447", "448", "449"], [], "import { message } from \"antd\";\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport Flag from \"../assets/tanzania-flag.png\";\r\nimport Logo2 from \"../assets/logo-2.png\";\r\nimport { getUserInfo } from \"../apicalls/users\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { SetUser } from \"../redux/usersSlice.js\";\r\nimport { useNavigate, useLocation } from \"react-router-dom\";\r\nimport { HideLoading, ShowLoading } from \"../redux/loaderSlice\";\r\nimport { checkPaymentStatus } from \"../apicalls/payment.js\";\r\nimport \"./ProtectedRoute.css\";\r\nimport { SetSubscription } from \"../redux/subscriptionSlice.js\";\r\nimport { setPaymentVerificationNeeded } from \"../redux/paymentSlice.js\";\r\nimport { useTheme } from \"../contexts/ThemeContext\";\r\n\r\nfunction ProtectedRoute({ children }) {\r\n  const { user } = useSelector((state) => state.user);\r\n  const [menu, setMenu] = useState([]);\r\n  const [collapsed, setCollapsed] = useState(false);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n  const [isPaymentPending, setIsPaymentPending] = useState(false);\r\n  const intervalRef = useRef(null);\r\n  const { subscriptionData } = useSelector((state) => state.subscription);\r\n  const { paymentVerificationNeeded } = useSelector((state) => state.payment);\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const activeRoute = location.pathname;\r\n\r\n  const userMenu = [\r\n    {\r\n      title: \"Hub\",\r\n      paths: [\"/user/hub\"],\r\n      icon: <i className=\"ri-apps-2-line\"></i>,\r\n      onClick: () => navigate(\"/user/hub\"),\r\n    },\r\n    {\r\n      title: \"Quiz\",\r\n      paths: [\"/user/quiz\", \"/user/write-exam\"],\r\n      icon: <i className=\"ri-pencil-line\"></i>,\r\n      onClick: () => navigate(\"/user/quiz\"),\r\n    },\r\n\r\n    {\r\n      title: \"Reports\",\r\n      paths: [\"/user/reports\"],\r\n      icon: <i className=\"ri-bar-chart-line\"></i>,\r\n      onClick: () => navigate(\"/user/reports\"),\r\n    },\r\n    {\r\n      title: \"Ranking\",\r\n      paths: [\"/user/ranking\"],\r\n      icon: <i className=\"ri-trophy-line\"></i>,\r\n      onClick: () => navigate(\"/user/ranking\"),\r\n    },\r\n    {\r\n      title: \"Study Material\",\r\n      paths: [\"/user/study-material\"],\r\n      icon: <i className=\"ri-book-open-line\"></i>,\r\n      onClick: () => navigate(\"/user/study-material\"),\r\n    },\r\n    {\r\n      title: \"About Us\",\r\n      paths: [\"/user/about-us\"],\r\n      icon: <i className=\"ri-information-line\"></i>,\r\n      onClick: () => navigate(\"/user/about-us\"),\r\n    },\r\n    {\r\n      title: \"Ask AI\",\r\n      paths: [\"/user/chat\"],\r\n      icon: <i className=\"ri-chat-smile-2-line\"></i>,\r\n      onClick: () => navigate(\"/user/chat\"),\r\n    },\r\n    {\r\n      title: \"Plans\",\r\n      paths: [\"/user/plans\"],\r\n      icon: <i className=\"ri-calendar-check-line\"></i>,\r\n      onClick: () => navigate(\"/user/plans\"),\r\n    },\r\n    {\r\n      title: \"Forum\",\r\n      paths: [\"/forum\"],\r\n      icon: <i className=\"ri-discuss-line\"></i>,\r\n      onClick: () => navigate(\"/forum\"),\r\n    },\r\n    {\r\n      title: \"Profile\",\r\n      paths: [\"/profile\"],\r\n      icon: <i className=\"ri-user-line\"></i>,\r\n      onClick: () => navigate(\"/profile\"),\r\n    },\r\n    {\r\n      title: \"Logout\",\r\n      paths: [\"/logout\"],\r\n      icon: <i className=\"ri-logout-box-line\"></i>,\r\n      onClick: () => {\r\n        localStorage.removeItem(\"token\");\r\n        navigate(\"/login\");\r\n      },\r\n    },\r\n  ];\r\n\r\n  const adminMenu = [\r\n    {\r\n      title: \"Users\",\r\n      paths: [\"/admin/users\", \"/admin/users/add\"],\r\n      icon: <i className=\"ri-file-list-line\"></i>,\r\n      onClick: () => navigate(\"/admin/users\"),\r\n    },\r\n    {\r\n      title: \"Exams\",\r\n      paths: [\"/admin/exams\", \"/admin/exams/add\"],\r\n      icon: <i className=\"ri-file-list-line\"></i>,\r\n      onClick: () => navigate(\"/admin/exams\"),\r\n    },\r\n    {\r\n      title: \"AI Questions\",\r\n      paths: [\"/admin/ai-questions\"],\r\n      icon: <i className=\"ri-robot-line\"></i>,\r\n      onClick: () => navigate(\"/admin/ai-questions\"),\r\n    },\r\n    {\r\n      title: \"Study Materials\",\r\n      paths: [\"/admin/study-materials\"],\r\n      icon: <i className=\"ri-book-line\"></i>,\r\n      onClick: () => navigate(\"/admin/study-materials\"),\r\n    },\r\n    {\r\n      title: \"Reports\",\r\n      paths: [\"/admin/reports\"],\r\n      icon: <i className=\"ri-bar-chart-line\"></i>,\r\n      onClick: () => navigate(\"/admin/reports\"),\r\n    },\r\n    {\r\n      title: \"Forum\",\r\n      paths: [\"/forum\"],\r\n      icon: <i className=\"ri-discuss-line\"></i>,\r\n      onClick: () => navigate(\"/forum\"),\r\n    },\r\n    {\r\n      title: \"Profile\",\r\n      paths: [\"/profile\"],\r\n      icon: <i className=\"ri-user-line\"></i>,\r\n      onClick: () => navigate(\"/profile\"),\r\n    },\r\n    {\r\n      title: \"Announcements\",\r\n      paths: [\"/admin/announcements\"],\r\n      icon: <i className=\"ri-notification-line\"></i>,\r\n      onClick: () => navigate(\"/admin/announcements\"),\r\n    },\r\n    {\r\n      title: \"Logout\",\r\n      paths: [\"/logout\"],\r\n      icon: <i className=\"ri-logout-box-line\"></i>,\r\n      onClick: () => {\r\n        localStorage.removeItem(\"token\");\r\n        navigate(\"/login\");\r\n      },\r\n    },\r\n  ];\r\n\r\n  const getUserData = async () => {\r\n    try {\r\n      console.log('Getting user data...'); // Debug log\r\n      const response = await getUserInfo();\r\n      console.log('User data response:', response); // Debug log\r\n\r\n      if (response.success) {\r\n        dispatch(SetUser(response.data));\r\n        if (response.data.isAdmin) {\r\n          setMenu(adminMenu);\r\n        } else {\r\n          setMenu(userMenu);\r\n        }\r\n      } else {\r\n        console.error('Failed to get user data:', response.message); // Debug log\r\n        message.error(response.message);\r\n        navigate(\"/login\");\r\n      }\r\n    } catch (error) {\r\n      console.error('Error getting user data:', error); // Debug log\r\n      navigate(\"/login\");\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Function to handle resizing\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n      setCollapsed(window.innerWidth < 768);\r\n    };\r\n\r\n    // Add resize event listener\r\n    window.addEventListener(\"resize\", handleResize);\r\n\r\n    if (window.innerWidth < 768) {\r\n      setIsMobile(true);\r\n      setCollapsed(true);\r\n    }\r\n\r\n    // Check for token and navigate\r\n    const token = localStorage.getItem(\"token\");\r\n    console.log('Token check:', token ? 'Token exists' : 'No token found'); // Debug log\r\n\r\n    if (token) {\r\n      getUserData();\r\n    } else {\r\n      console.log('No token, redirecting to login'); // Debug log\r\n      navigate(\"/login\");\r\n    }\r\n\r\n    // Cleanup the event listener when the component is unmounted\r\n    return () => {\r\n      window.removeEventListener(\"resize\", handleResize);\r\n    };\r\n  }, []);\r\n\r\n  const getIsActiveOrNot = (paths) => {\r\n    if (paths.includes(activeRoute)) {\r\n      return true;\r\n    } else {\r\n      if (\r\n        activeRoute.includes(\"/admin/exams/edit\") &&\r\n        paths.includes(\"/admin/exams\")\r\n      ) {\r\n        return true;\r\n      }\r\n      if (\r\n        activeRoute.includes(\"/user/write-exam\") &&\r\n        paths.includes(\"/user/write-exam\")\r\n      ) {\r\n        return true;\r\n      }\r\n    }\r\n    return false;\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {\r\n      navigate('/user/plans');\r\n    }\r\n  }, [isPaymentPending, activeRoute, navigate]);\r\n\r\n  const verifyPaymentStatus = async () => {\r\n    try {\r\n      const data = await checkPaymentStatus();\r\n      console.log(\"Payment Status:\", data);\r\n      if (data?.error || data?.paymentStatus !== 'paid') {\r\n        if (subscriptionData !== null) {\r\n          dispatch(SetSubscription(null));\r\n        }\r\n        setIsPaymentPending(true);\r\n      }\r\n      else {\r\n        setIsPaymentPending(false);\r\n        dispatch(SetSubscription(data));\r\n        if (intervalRef.current) {\r\n          clearInterval(intervalRef.current);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Error checking payment status:\", error);\r\n      dispatch(SetSubscription(null));\r\n      setIsPaymentPending(true);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing 2222222...\");\r\n\r\n      if (paymentVerificationNeeded) {\r\n        console.log('Inside timer in effect 2....');\r\n        intervalRef.current = setInterval(() => {\r\n          console.log('Timer in action...');\r\n          verifyPaymentStatus();\r\n        }, 15000);\r\n        dispatch(setPaymentVerificationNeeded(false));\r\n      }\r\n    }\r\n  }, [paymentVerificationNeeded]);\r\n\r\n  useEffect(() => {\r\n    if (user?.paymentRequired && !user?.isAdmin) {\r\n      console.log(\"Effect Runing...\");\r\n      verifyPaymentStatus();\r\n    }\r\n  }, [user, activeRoute]);\r\n\r\n\r\n  const getButtonClass = (title) => {\r\n    // Exclude \"Plans\" and \"Profile\" buttons from the \"button-disabled\" class\r\n    if (!user.paymentRequired || title === \"Plans\" || title === \"Profile\" || title === \"Logout\") {\r\n      return \"\"; // No class applied\r\n    }\r\n\r\n    return subscriptionData?.paymentStatus !== \"paid\" && user?.paymentRequired\r\n      ? \"button-disabled\"\r\n      : \"\";\r\n  };\r\n\r\n\r\n  const { isDarkMode, toggleTheme } = useTheme();\r\n\r\n  return (\r\n    <div className=\"layout-modern min-h-screen flex\">\r\n      {/* Modern Sidebar */}\r\n      <AnimatePresence>\r\n        {(!collapsed || !isMobile) && (\r\n          <motion.div\r\n            initial={{ x: -300, opacity: 0 }}\r\n            animate={{ x: 0, opacity: 1 }}\r\n            exit={{ x: -300, opacity: 0 }}\r\n            transition={{ duration: 0.3, ease: \"easeInOut\" }}\r\n            className={`sidebar-modern ${isMobile ? \"fixed inset-y-0 left-0 z-50 w-64\" : \"w-64\"} ${\r\n              collapsed && isMobile ? \"hidden\" : \"\"\r\n            }`}\r\n          >\r\n            <div className=\"flex flex-col h-full\">\r\n              {/* Sidebar Header */}\r\n              <div className=\"p-6 border-b border-white/10\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <img\r\n                      src={Logo2}\r\n                      alt=\"brainwave\"\r\n                      className=\"w-8 h-8 rounded-lg\"\r\n                    />\r\n                    {!collapsed && (\r\n                      <div className=\"text-white font-bold text-lg\">\r\n                        BRAIN<span className=\"text-green-400\">WAVE</span>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                  {isMobile && (\r\n                    <button\r\n                      onClick={() => setCollapsed(true)}\r\n                      className=\"text-white/80 hover:text-white p-1 rounded-lg hover:bg-white/10 transition-colors\"\r\n                    >\r\n                      <i className=\"ri-close-line text-xl\"></i>\r\n                    </button>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Navigation Menu */}\r\n              <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\r\n                {menu.map((item, index) => {\r\n                  const isActive = getIsActiveOrNot(item.paths);\r\n                  const isDisabled = getButtonClass(item.title) === \"button-disabled\";\r\n\r\n                  return (\r\n                    <motion.div\r\n                      key={index}\r\n                      whileHover={{ x: isDisabled ? 0 : 4 }}\r\n                      whileTap={{ scale: isDisabled ? 1 : 0.95 }}\r\n                    >\r\n                      <button\r\n                        className={`sidebar-item w-full text-left ${\r\n                          isActive ? \"sidebar-item-active\" : \"\"\r\n                        } ${isDisabled ? \"opacity-50 cursor-not-allowed\" : \"\"}`}\r\n                        onClick={!isDisabled ? item.onClick : undefined}\r\n                        disabled={isDisabled}\r\n                      >\r\n                        <span className=\"text-xl mr-3\">{item.icon}</span>\r\n                        {!collapsed && (\r\n                          <span className=\"font-medium\">{item.title}</span>\r\n                        )}\r\n                        {isActive && !collapsed && (\r\n                          <motion.div\r\n                            layoutId=\"activeIndicator\"\r\n                            className=\"ml-auto w-2 h-2 bg-white rounded-full\"\r\n                          />\r\n                        )}\r\n                      </button>\r\n                    </motion.div>\r\n                  );\r\n                })}\r\n              </nav>\r\n\r\n              {/* Sidebar Footer */}\r\n              {!collapsed && (\r\n                <div className=\"p-4 border-t border-white/10\">\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <img\r\n                      src={Flag}\r\n                      alt=\"tanzania-flag\"\r\n                      className=\"w-8 h-6 rounded\"\r\n                    />\r\n                    <div className=\"text-white/80 text-sm\">\r\n                      <div className=\"font-medium\">{user?.name}</div>\r\n                      <div className=\"text-xs text-white/60\">\r\n                        {user?.isAdmin ? \"Administrator\" : \"Student\"}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n      {/* Mobile Overlay */}\r\n      {isMobile && !collapsed && (\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          exit={{ opacity: 0 }}\r\n          className=\"fixed inset-0 bg-black/50 z-40\"\r\n          onClick={() => setCollapsed(true)}\r\n        />\r\n      )}\r\n\r\n      {/* Main Content Area */}\r\n      <div className={`flex-1 flex flex-col min-h-screen ${isMobile ? \"\" : collapsed ? \"ml-0\" : \"ml-0\"}`}>\r\n        {/* Modern Header */}\r\n        <motion.header\r\n          initial={{ y: -20, opacity: 0 }}\r\n          animate={{ y: 0, opacity: 1 }}\r\n          className=\"nav-modern bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-30\"\r\n        >\r\n          <div className=\"px-4 sm:px-6 lg:px-8\">\r\n            <div className=\"flex items-center justify-between h-16\">\r\n              {/* Left Section */}\r\n              <div className=\"flex items-center space-x-4\">\r\n                {/* Mobile Menu Button */}\r\n                {isMobile && (\r\n                  <button\r\n                    onClick={() => setCollapsed(false)}\r\n                    className=\"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\"\r\n                  >\r\n                    <i className=\"ri-menu-line text-xl\"></i>\r\n                  </button>\r\n                )}\r\n\r\n                {/* Desktop Collapse Button */}\r\n                {!isMobile && (\r\n                  <button\r\n                    onClick={() => setCollapsed(!collapsed)}\r\n                    className=\"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\"\r\n                  >\r\n                    <i className={`ri-${collapsed ? 'menu' : 'close'}-line text-xl`}></i>\r\n                  </button>\r\n                )}\r\n\r\n                {/* Logo for mobile when sidebar is collapsed */}\r\n                {isMobile && collapsed && (\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <img src={Logo2} alt=\"brainwave\" className=\"w-8 h-8\" />\r\n                    <span className=\"font-bold text-gray-900\">\r\n                      BRAIN<span className=\"text-primary-600\">WAVE</span>\r\n                    </span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Right Section */}\r\n              <div className=\"flex items-center space-x-4\">\r\n                {/* Dark Mode Toggle */}\r\n                <button\r\n                  onClick={toggleTheme}\r\n                  className=\"p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors\"\r\n                >\r\n                  <i className={`ri-${isDarkMode ? 'sun' : 'moon'}-line text-xl`}></i>\r\n                </button>\r\n\r\n                {/* User Profile */}\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div className=\"hidden sm:block text-right\">\r\n                    <div className=\"text-sm font-medium text-gray-900\">{user?.name}</div>\r\n                    <div className=\"text-xs text-gray-500\">\r\n                      {user?.isAdmin ? \"Administrator\" : \"Student\"}\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-primary-600 font-medium text-sm\">\r\n                      {user?.name?.charAt(0)?.toUpperCase()}\r\n                    </span>\r\n                  </div>\r\n                  <img src={Flag} alt=\"tanzania-flag\" className=\"w-6 h-4 rounded-sm\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.header>\r\n\r\n        {/* Page Content */}\r\n        <main className=\"flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3, delay: 0.1 }}\r\n            className=\"h-full\"\r\n          >\r\n            {children}\r\n          </motion.div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ProtectedRoute;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\usersSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\loaderSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\subscriptionSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\redux\\paymentSlice.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\Announcement\\AnnouncementModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Announcement\\Announcement.jsx", ["450"], [], "import React, { useEffect, useState } from \"react\";\r\nimport { message, Modal, Input } from \"antd\";\r\nimport {\r\n  PlusOutlined,\r\n  EditOutlined,\r\n  DeleteOutlined,\r\n} from \"@ant-design/icons\";\r\nimport {\r\n  addAnnouncement,\r\n  deleteAnnouncement,\r\n  getAnnouncements,\r\n  updateAnnouncement,\r\n} from \"../../../apicalls/announcements\";\r\nimport \"./announcement.css\"; // your custom styles\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { useDispatch } from \"react-redux\";\r\n\r\nexport default function Announcement() {\r\n  const [list, setList] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [modalOpen, setModalOpen] = useState(false);\r\n  const [form, setForm] = useState({ heading: \"\", description: \"\" });\r\n  const [editingId, setEditingId] = useState(null);\r\n\r\n  const dispatch = useDispatch();\r\n\r\n  const fetchAll = async () => {\r\n    dispatch(ShowLoading());\r\n    const res = await getAnnouncements();\r\n    dispatch(HideLoading());\r\n    if (res.success !== false) setList(res);\r\n    else message.error(res.error || \"Failed to load announcements\");\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchAll();\r\n  }, []);\r\n\r\n  const openAddModal = () => {\r\n    setForm({ heading: \"\", description: \"\" });\r\n    setEditingId(null);\r\n    setModalOpen(true);\r\n  };\r\n\r\n  const openEditModal = (announcement) => {\r\n    setForm({\r\n      heading: announcement.heading,\r\n      description: announcement.description,\r\n    });\r\n    setEditingId(announcement._id);\r\n    setModalOpen(true);\r\n  };\r\n\r\n  const handleModalSubmit = async () => {\r\n    if (!form.heading || !form.description) {\r\n      return message.warning(\"Heading and Description are required\");\r\n    }\r\n    dispatch(ShowLoading());\r\n\r\n    setLoading(true);\r\n    const res = editingId\r\n      ? await updateAnnouncement(editingId, form)\r\n      : await addAnnouncement(form);\r\n    setLoading(false);\r\n\r\n    if (res.success === false)\r\n      return message.error(res.error || \"Operation failed\");\r\n\r\n    message.success(editingId ? \"Announcement updated\" : \"Announcement added\");\r\n    setModalOpen(false);\r\n    setForm({ heading: \"\", description: \"\" });\r\n    setEditingId(null);\r\n    fetchAll();\r\n    dispatch(HideLoading());\r\n\r\n  };\r\n\r\n  const handleDelete = async (id) => {\r\n    if (!window.confirm(\"Delete this announcement?\")) return;\r\n    dispatch(ShowLoading());\r\n    const res = await deleteAnnouncement(id);\r\n    dispatch(HideLoading());\r\n\r\n    if (res.success === false)\r\n      return message.error(res.error || \"Delete failed\");\r\n    message.success(\"Announcement deleted\");\r\n    fetchAll();\r\n  };\r\n\r\n  return (\r\n    <div className=\"announcement-admin\">\r\n      <div className=\"admin-header\">\r\n        <h2>Manage Announcements</h2>\r\n        <button onClick={openAddModal} className=\"add-btn\">\r\n          <PlusOutlined />\r\n          <span>Add Announcement</span>\r\n        </button>\r\n      </div>\r\n\r\n      {list.length === 0 ? (\r\n        <p className=\"no-announcements\">No announcements yet.</p>\r\n      ) : (\r\n        <div className=\"announcement-list\">\r\n          {list.map((item) => (\r\n            <div className=\"announcement-card\" key={item._id}>\r\n              <div className=\"announcement-content\">\r\n                <h3>{item.heading}</h3>\r\n                <p>{item.description}</p>\r\n              </div>\r\n              <div className=\"card-actions\">\r\n                <button\r\n                  onClick={() => openEditModal(item)}\r\n                  className=\"edit-btn\"\r\n                  title=\"Edit\"\r\n                >\r\n                  <EditOutlined />\r\n                </button>\r\n                <button\r\n                  onClick={() => handleDelete(item._id)}\r\n                  className=\"delete-btn\"\r\n                  title=\"Delete\"\r\n                >\r\n                  <DeleteOutlined />\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      {/* Modal for Add/Edit */}\r\n      <Modal\r\n        title={editingId ? \"Edit Announcement\" : \"Add Announcement\"}\r\n        open={modalOpen}\r\n        onCancel={() => setModalOpen(false)}\r\n        onOk={handleModalSubmit}\r\n        okText={editingId ? \"Update\" : \"Add\"}\r\n        confirmLoading={loading}\r\n      >\r\n        <div className=\"modal-form\">\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Heading\"\r\n            style={{ borderRadius: \"5px\" }}\r\n            value={form.heading}\r\n            onChange={(e) => setForm({ ...form, heading: e.target.value })}\r\n          />\r\n          <Input.TextArea\r\n            placeholder=\"Description\"\r\n            rows={4}\r\n            value={form.description}\r\n            onChange={(e) =>\r\n              setForm({ ...form, description: e.target.value })\r\n            }\r\n          />\r\n        </div>\r\n      </Modal>\r\n    </div>\r\n  );\r\n}\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditExam.js", ["451", "452"], [], "import { Col, Form, message, Row, Select, Table } from \"antd\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport {\r\n  addExam,\r\n  deleteQuestionById,\r\n  editExamById,\r\n  getExamById,\r\n} from \"../../../apicalls/exams\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\n\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Tabs } from \"antd\";\r\nimport AddEditQuestion from \"./AddEditQuestion\";\r\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\r\nconst { TabPane } = Tabs;\r\n\r\nfunction AddEditExam() {\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [examData, setExamData] = useState(null);\r\n  const [level, setLevel] = useState('');\r\n  const [showAddEditQuestionModal, setShowAddEditQuestionModal] = useState(false);\r\n  const [selectedQuestion, setSelectedQuestion] = useState(null);\r\n  const [classValue, setClassValue] = useState('');\r\n  const params = useParams();\r\n\r\n  console.log(examData?.questions, \"examData?.questions\")\r\n\r\n  const onFinish = async (values) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      let response;\r\n\r\n      if (params.id) {\r\n        response = await editExamById({\r\n          ...values,\r\n          examId: params.id,\r\n        });\r\n      } else {\r\n        response = await addExam(values);\r\n      }\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        navigate(\"/admin/exams\");\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const getExamData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getExamById({\r\n        examId: params.id,\r\n      });\r\n      setClassValue(response?.data?.class);\r\n      setLevel(response?.data?.level);\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setExamData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (params.id) {\r\n      getExamData();\r\n    }\r\n  }, []);\r\n\r\n  const deleteQuestion = async (questionId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteQuestionById({\r\n        questionId,\r\n        examId: params.id\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getExamData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const questionsColumns = [\r\n    {\r\n      title: \"Question\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"Options\",\r\n      dataIndex: \"options\",\r\n      render: (text, record) => {\r\n        if (record?.options && typeof record.options === 'object' && Object.keys(record.options).length > 0) {\r\n          return Object.keys(record.options).map((key) => (\r\n            <div key={key}>\r\n              {key}: {record.options[key]}\r\n            </div>\r\n          ));\r\n        } else {\r\n          return <div>No options available for this question.</div>;\r\n        }\r\n      },\r\n    },\r\n    {\r\n      title: \"Correct Answer\",\r\n      dataIndex: \"correctOption\",\r\n      render: (text, record) => {\r\n        if (record.answerType === \"Free Text\") {\r\n          return <div>{record.correctOption}</div>;\r\n        } else {\r\n          return (\r\n            <div>\r\n              {record.correctOption}: {record.options && record.options[record.correctOption] ? record.options[record.correctOption] : record.correctOption}\r\n            </div>\r\n          );\r\n        }\r\n      },\r\n    },\r\n    {\r\n      title: \"Source\",\r\n      dataIndex: \"source\",\r\n      render: (text, record) => (\r\n        <div className=\"flex items-center gap-1\">\r\n          {record?.isAIGenerated ? (\r\n            <span className=\"flex items-center gap-1 text-blue-600 text-sm\">\r\n              🤖 AI\r\n            </span>\r\n          ) : (\r\n            <span className=\"text-gray-600 text-sm\">Manual</span>\r\n          )}\r\n          {(record?.image || record?.imageUrl) && (\r\n            <span title=\"Has Image\">🖼️</span>\r\n          )}\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex gap-2 items-center\">\r\n          {/* Edit Button */}\r\n          <i\r\n            className=\"ri-pencil-line cursor-pointer text-blue-600 hover:text-blue-800\"\r\n            title=\"Edit Question\"\r\n            onClick={() => {\r\n              setSelectedQuestion(record);\r\n              setShowAddEditQuestionModal(true);\r\n            }}\r\n          ></i>\r\n\r\n          {/* Add Image Button for AI-generated questions without images */}\r\n          {record?.isAIGenerated && !record?.image && !record?.imageUrl && (\r\n            <i\r\n              className=\"ri-image-add-line cursor-pointer text-green-600 hover:text-green-800\"\r\n              title=\"Add Image to AI Question\"\r\n              onClick={() => {\r\n                setSelectedQuestion(record);\r\n                setShowAddEditQuestionModal(true);\r\n              }}\r\n            ></i>\r\n          )}\r\n\r\n          {/* AI Generated Indicator */}\r\n          {record?.isAIGenerated && (\r\n            <span\r\n              className=\"text-blue-500 text-sm\"\r\n              title=\"AI Generated Question\"\r\n            >\r\n              🤖\r\n            </span>\r\n          )}\r\n\r\n          {/* Image Indicator */}\r\n          {(record?.image || record?.imageUrl) && (\r\n            <span\r\n              className=\"text-green-500 text-sm\"\r\n              title=\"Has Image\"\r\n            >\r\n              🖼️\r\n            </span>\r\n          )}\r\n\r\n          {/* Delete Button */}\r\n          <i\r\n            className=\"ri-delete-bin-line cursor-pointer text-red-600 hover:text-red-800\"\r\n            title=\"Delete Question\"\r\n            onClick={() => {\r\n              deleteQuestion(record._id);\r\n            }}\r\n          ></i>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const handleLevelChange = (e) => {\r\n    setLevel(e.target.value);\r\n    setClassValue(\"\"); // Reset class\r\n  };\r\n\r\n  console.log(classValue, \"classValue\")\r\n\r\n\r\n\r\n  return (\r\n    <div>\r\n      <PageTitle title={params.id ? \"Edit Exam\" : \"Add Exam\"} />\r\n      <div className=\"divider\"></div>\r\n\r\n      {(examData || !params.id) && (\r\n        <Form layout=\"vertical\" onFinish={onFinish} initialValues={examData}>\r\n          <Tabs defaultActiveKey=\"1\">\r\n            <TabPane tab=\"Exam Details\" key=\"1\">\r\n              <Row gutter={[10, 10]}>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Exam Name\" name=\"name\">\r\n                    <input type=\"text\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Exam Duration (Seconds)\" name=\"duration\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n\r\n\r\n                <Col span={8}>\r\n                  <Form.Item name=\"level\" label=\"Level\" initialValue=\"\">\r\n                    <select value={level} onChange={handleLevelChange}   >\r\n                      <option value=\"\" disabled >\r\n                        Select Level\r\n                      </option>\r\n                      <option value=\"Primary\">Primary</option>\r\n                      <option value=\"Secondary\">Secondary</option>\r\n                      <option value=\"Advance\">Advance</option>\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Category\" name=\"category\">\r\n                    <select name=\"\" id=\"\">\r\n                      <option value=\"\">Select Category</option>\r\n                      {level === \"primary\" && (\r\n                        <>\r\n                          {primarySubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                      {level === \"secondary\" && (\r\n                        <>\r\n                          {secondarySubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                      {level === \"advance\" && (\r\n                        <>\r\n                          {advanceSubjects.map((subject, index) => (\r\n                            <option key={index} value={subject}>\r\n                              {subject}\r\n                            </option>\r\n                          ))}\r\n                        </>\r\n                      )}\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n\r\n                <Col span={8}>\r\n\r\n                  <Form.Item name=\"class\" label=\"Class\" initialValue=\"\" required>\r\n                    <select value={classValue} onChange={(e) => setClassValue(e.target.value)}>\r\n                      <option value=\"\"  >\r\n                        Select Class\r\n                      </option>\r\n                      {level === \"primary\" && (\r\n                        <>\r\n                          <option value=\"1\">1</option>\r\n                          <option value=\"2\">2</option>\r\n                          <option value=\"3\">3</option>\r\n                          <option value=\"4\">4</option>\r\n                          <option value=\"5\">5</option>\r\n                          <option value=\"6\">6</option>\r\n                          <option value=\"7\">7</option>\r\n                        </>\r\n                      )}\r\n                      {level === \"secondary\" && (\r\n                        <>\r\n                          <option value=\"Form-1\">Form-1</option>\r\n                          <option value=\"Form-2\">Form-2</option>\r\n                          <option value=\"Form-3\">Form-3</option>\r\n                          <option value=\"Form-4\">Form-4</option>\r\n                        </>\r\n                      )}\r\n                      {level === \"advance\" && (\r\n                        <>\r\n                          <option value=\"Form-5\">Form-5</option>\r\n                          <option value=\"Form-6\">Form-6</option>\r\n                        </>\r\n                      )}\r\n                    </select>\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Total Marks\" name=\"totalMarks\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n                <Col span={8}>\r\n                  <Form.Item label=\"Passing Marks\" name=\"passingMarks\">\r\n                    <input type=\"number\" />\r\n                  </Form.Item>\r\n                </Col>\r\n              </Row>\r\n              <div className=\"flex justify-end gap-2\">\r\n                <button\r\n                  className=\"primary-outlined-btn\"\r\n                  type=\"button\"\r\n                  onClick={() => navigate(\"/admin/exams\")}\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button className=\"primary-contained-btn\" type=\"submit\">\r\n                  Save\r\n                </button>\r\n              </div>\r\n            </TabPane>\r\n            {params.id && (\r\n              <TabPane tab=\"Questions\" key=\"2\">\r\n                <div className=\"flex justify-end\">\r\n                  <button\r\n                    className=\"primary-outlined-btn\"\r\n                    type=\"button\"\r\n                    onClick={() => setShowAddEditQuestionModal(true)}\r\n                  >\r\n                    Add Question\r\n                  </button>\r\n                </div>\r\n\r\n                <Table\r\n                  columns={questionsColumns}\r\n                  dataSource={examData?.questions || []}\r\n                />\r\n              </TabPane>\r\n            )}\r\n          </Tabs>\r\n        </Form>\r\n      )}\r\n\r\n      {showAddEditQuestionModal && (\r\n        <AddEditQuestion\r\n          setShowAddEditQuestionModal={setShowAddEditQuestionModal}\r\n          showAddEditQuestionModal={showAddEditQuestionModal}\r\n          examId={params.id}\r\n          refreshData={getExamData}\r\n          selectedQuestion={selectedQuestion}\r\n          setSelectedQuestion={setSelectedQuestion}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AddEditExam;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\index.js", ["453"], [], "import { message, Table } from \"antd\";\r\nimport React, { useEffect } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { deleteExamById, getAllExams } from \"../../../apicalls/exams\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nfunction Exams() {\r\n  const navigate = useNavigate();\r\n  const [exams, setExams] = React.useState([]);\r\n  const dispatch = useDispatch();\r\n\r\n  const getExamsData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllExams();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setExams(response.data.reverse());\r\n        console.log(response, \"exam\");\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteExam = async (examId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteExamById({\r\n        examId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getExamsData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"Duration\",\r\n      dataIndex: \"duration\",\r\n    },\r\n    {\r\n      title: \"Class\",\r\n      dataIndex: \"class\",\r\n    },\r\n    {\r\n      title: \"Category\",\r\n      dataIndex: \"category\",\r\n    },\r\n    {\r\n      title: \"Total Marks\",\r\n      dataIndex: \"totalMarks\",\r\n    },\r\n    {\r\n      title: \"Passing Marks\",\r\n      dataIndex: \"passingMarks\",\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex gap-2\">\r\n          <i\r\n            className=\"ri-pencil-line\"\r\n            onClick={() => navigate(`/admin/exams/edit/${record._id}`)}\r\n          ></i>\r\n          <i\r\n            className=\"ri-delete-bin-line\"\r\n            onClick={() => deleteExam(record._id)}\r\n          ></i>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n  useEffect(() => {\r\n    getExamsData();\r\n  }, []);\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between mt-2 items-end\">\r\n        <PageTitle title=\"Exams\" />\r\n\r\n        <button\r\n          className=\"primary-outlined-btn flex items-center\"\r\n          onClick={() => navigate(\"/admin/exams/add\")}\r\n        >\r\n          <i className=\"ri-add-line\"></i>\r\n          Add Exam\r\n        </button>\r\n      </div>\r\n      <div className=\"divider\"></div>\r\n\r\n      <Table columns={columns} dataSource={exams} />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Exams;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\Plans.jsx", ["454"], [], "import React, { useEffect, useState } from \"react\";\r\nimport { getPlans } from \"../../../apicalls/plans\";\r\nimport \"./Plans.css\";\r\nimport ConfirmModal from \"./components/ConfirmModal\";\r\nimport WaitingModal from \"./components/WaitingModal\";\r\nimport { addPayment } from \"../../../apicalls/payment\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { setPaymentVerificationNeeded } from \"../../../redux/paymentSlice\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nconst Plans = () => {\r\n    const [plans, setPlans] = useState([]);\r\n    const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);\r\n    const [isWaitingModalOpen, setWaitingModalOpen] = useState(false);\r\n    const [paymentInProgress, setPaymentInProgress] = useState(false);\r\n    const [selectedPlan, setSelectedPlan] = useState(null);\r\n    const { user } = useSelector((state) => state.user);\r\n    const { subscriptionData } = useSelector((state) => state.subscription);\r\n    const dispatch = useDispatch()\r\n\r\n    useEffect(() => {\r\n        const fetchPlans = async () => {\r\n            try {\r\n                const response = await getPlans();\r\n                setPlans(response);\r\n            } catch (error) {\r\n                console.error(\"Error fetching plans:\", error);\r\n            }\r\n        };\r\n\r\n        fetchPlans();\r\n    }, []);\r\n\r\n    const transactionDetails = {\r\n        amount: selectedPlan?.discountedPrice || 'N/A',\r\n        currency: \"TZS\",\r\n        destination: \"brainwave.zone\",\r\n    };\r\n\r\n\r\n    const handlePaymentStart = async (plan) => {\r\n        setSelectedPlan(plan);\r\n        try {\r\n            dispatch(ShowLoading());\r\n            const response = await addPayment({ plan });\r\n            localStorage.setItem(\"order_id\", response.order_id);\r\n            setWaitingModalOpen(true);\r\n            setPaymentInProgress(true);\r\n            dispatch(setPaymentVerificationNeeded(true));\r\n        } catch (error) {\r\n            console.error(\"Error processing payment:\", error);\r\n        } finally {\r\n            dispatch(HideLoading());\r\n        }\r\n    };\r\n\r\n\r\n    useEffect(() => {\r\n        console.log(\"subscription Data in Plans\", subscriptionData)\r\n        if (user?.paymentRequired === true && subscriptionData?.paymentStatus === \"paid\" && paymentInProgress) {\r\n            setWaitingModalOpen(false);\r\n            setConfirmModalOpen(true);\r\n            setPaymentInProgress(false);\r\n        }\r\n    }, [user, subscriptionData]);\r\n\r\n    return (\r\n        <div>\r\n            {!user ?\r\n                <>\r\n                </>\r\n                :\r\n                !user.paymentRequired ?\r\n                    <div className=\"no-plan-required\">\r\n                        <div className=\"no-plan-content\">\r\n                            <h2>No Plan Required</h2>\r\n                            <p>You don't need to buy any plan to access the system. Enjoy all the features with no additional cost!</p>\r\n                        </div>\r\n                    </div>\r\n                    :\r\n                    subscriptionData?.paymentStatus !== \"paid\" ?\r\n                        <div className=\"plans-container\">\r\n                            {plans.map((plan) => (\r\n                                <div\r\n                                    key={plan._id}\r\n                                    className={`plan-card ${plan.title === \"Standard Membership\" ? \"basic\" : \"\"}`}\r\n                                >\r\n                                    {plan.title === \"Standard Membership\" && (\r\n                                        <div className=\"most-popular-label\">MOST POPULAR</div>\r\n                                    )}\r\n\r\n                                    <h2 className=\"plan-title\">{plan.title}</h2>\r\n                                    <p className=\"plan-actual-price\">\r\n                                        {plan.actualPrice.toLocaleString()} TZS\r\n                                    </p>\r\n                                    <p className=\"plan-discounted-price\">\r\n                                        {plan.discountedPrice.toLocaleString()} TZS\r\n                                    </p>\r\n                                    <span className=\"plan-discount-tag\">\r\n                                        {plan.discountPercentage}% OFF\r\n                                    </span>\r\n                                    <p className=\"plan-renewal-info\">\r\n                                        For {plan?.features[0]}\r\n                                    </p>\r\n                                    <button className=\"plan-button\"\r\n                                        // onClick={() => setConfirmModalOpen(true)}\r\n                                        onClick={() => handlePaymentStart(plan)}\r\n                                    >Choose Plan</button>\r\n                                    <ul className=\"plan-features\">\r\n                                        {plan.features.map((feature, index) => (\r\n                                            <li key={index} className=\"plan-feature\">\r\n                                                <span className=\"plan-feature-icon\">✔</span>\r\n                                                {feature}\r\n                                            </li>\r\n                                        ))}\r\n                                    </ul>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                        :\r\n                        <div className=\"subscription-details\">\r\n                            <h1 className=\"plan-title\">{subscriptionData.plan.title}</h1>\r\n\r\n                            <svg\r\n                                width=\"64px\"\r\n                                height=\"64px\"\r\n                                viewBox=\"-3.2 -3.2 38.40 38.40\"\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                fill=\"#10B981\"\r\n                                stroke=\"#253864\"\r\n                                transform=\"matrix(1, 0, 0, 1, 0, 0)\"\r\n                            >\r\n                                <g id=\"SVGRepo_bgCarrier\" strokeWidth=\"0\"></g>\r\n                                <g id=\"SVGRepo_tracerCarrier\" strokeLinecap=\"round\" strokeLinejoin=\"round\" stroke=\"#CCCCCC\" strokeWidth=\"0.064\"></g>\r\n                                <g id=\"SVGRepo_iconCarrier\">\r\n                                    <path\r\n                                        d=\"m16 0c8.836556 0 16 7.163444 16 16s-7.163444 16-16 16-16-7.163444-16-16 7.163444-16 16-16zm5.7279221 11-7.0710679 7.0710678-4.2426406-4.2426407-1.4142136 1.4142136 5.6568542 5.6568542 8.4852814-8.4852813z\"\r\n                                        fill=\"#202327\"\r\n                                        fillRule=\"evenodd\"\r\n                                    ></path>\r\n                                </g>\r\n                            </svg>\r\n\r\n                            <p className=\"plan-description\">{subscriptionData?.plan?.subscriptionData}</p>\r\n                            <p className=\"plan-dates\">Start Date: {subscriptionData.startDate}</p>\r\n                            <p className=\"plan-dates\">End Date: {subscriptionData.endDate}</p>\r\n                        </div>\r\n            }\r\n\r\n            <WaitingModal\r\n                isOpen={isWaitingModalOpen}\r\n                onClose={() => setWaitingModalOpen(false)}\r\n            />\r\n\r\n            <ConfirmModal\r\n                isOpen={isConfirmModalOpen}\r\n                onClose={() => setConfirmModalOpen(false)}\r\n                transaction={transactionDetails}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Plans;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\index.js", ["455", "456", "457", "458", "459", "460", "461", "462", "463", "464", "465", "466"], [], "import { message } from \"antd\";\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { getExamById } from \"../../../apicalls/exams\";\r\nimport { addReport } from \"../../../apicalls/reports\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport Instructions from \"./Instructions\";\r\nimport Pass from \"../../../assets/pass.gif\";\r\nimport Fail from \"../../../assets/fail.gif\";\r\nimport Confetti from \"react-confetti\";\r\nimport useWindowSize from \"react-use/lib/useWindowSize\";\r\nimport PassSound from \"../../../assets/pass.mp3\";\r\nimport { QuizQuest<PERSON>, QuizTimer, QuizTimer<PERSON><PERSON>lay, Card, Button, Loading } from \"../../../components/modern\";\r\nimport { TbClock, TbQuestionMark, TbCheck, TbX, TbFlag, TbArrowLeft, TbArrowRight, TbBrain } from \"react-icons/tb\";\r\nimport FailSound from \"../../../assets/fail.mp3\";\r\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\r\nimport ContentRenderer from \"../../../components/ContentRenderer\";\r\n\r\nimport QuizRenderer from \"../../../components/QuizRenderer\";\r\n\r\nfunction WriteExam() {\r\n  const [examData, setExamData] = useState(null);\r\n  const [questions, setQuestions] = useState([]);\r\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\r\n  const [selectedOptions, setSelectedOptions] = useState({});\r\n  const [result, setResult] = useState({});\r\n  const params = useParams();\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [view, setView] = useState(\"instructions\");\r\n  const [secondsLeft, setSecondsLeft] = useState(0);\r\n  const [timeUp, setTimeUp] = useState(false);\r\n  const [intervalId, setIntervalId] = useState(null);\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const { width, height } = useWindowSize();\r\n  const [explanations, setExplanations] = useState({});\r\n\r\n  const getExamData = useCallback(async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getExamById({ examId: params.id });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setQuestions(response.data?.questions || []);\r\n        setExamData(response.data);\r\n        setSecondsLeft(response.data?.duration || 0);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  }, [params.id, dispatch]);\r\n\r\n  const checkFreeTextAnswers = async (payload) => {\r\n    if (!payload.length) return [];\r\n    const { data } = await chatWithChatGPTToGetAns(payload);\r\n    return data;\r\n  };\r\n\r\n  const calculateResult = useCallback(async () => {\r\n    try {\r\n      // Check if user is available\r\n      if (!user || !user._id) {\r\n        message.error(\"User not found. Please log in again.\");\r\n        navigate(\"/login\");\r\n        return;\r\n      }\r\n\r\n      dispatch(ShowLoading());\r\n\r\n      const freeTextPayload = [];\r\n      const indexMap = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          indexMap.push(idx);\r\n          freeTextPayload.push({\r\n            question: q.name,\r\n            expectedAnswer: q.correctAnswer || q.correctOption,\r\n            userAnswer: selectedOptions[idx] || \"\",\r\n          });\r\n        }\r\n      });\r\n\r\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\r\n      const gptMap = {};\r\n\r\n      gptResults.forEach((r) => {\r\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = r.result;\r\n        } else if (typeof r.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\r\n        }\r\n      });\r\n\r\n      const correctAnswers = [];\r\n      const wrongAnswers = [];\r\n      const wrongPayload = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        const userAnswerKey = selectedOptions[idx] || \"\";\r\n\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\r\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: q.correctAnswer || q.correctOption,\r\n              userAnswer: userAnswerKey,\r\n            });\r\n          }\r\n        } else if (q.answerType === \"Options\") {\r\n          const correctKey = q.correctOption;\r\n          const correctValue = (q.options && q.options[correctKey]) || correctKey;\r\n          const userValue = (q.options && q.options[userAnswerKey]) || userAnswerKey || \"\";\r\n\r\n          const isCorrect = correctKey === userAnswerKey;\r\n          const enriched = { ...q, userAnswer: userAnswerKey };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: correctValue,\r\n              userAnswer: userValue,\r\n            });\r\n          }\r\n        }\r\n      });\r\n\r\n      const verdict = correctAnswers.length >= examData.passingMarks ? \"Pass\" : \"Fail\";\r\n      const tempResult = {\r\n        correctAnswers: correctAnswers || [],\r\n        wrongAnswers: wrongAnswers || [],\r\n        verdict: verdict || \"Fail\"\r\n      };\r\n\r\n      setResult(tempResult);\r\n\r\n      const response = await addReport({\r\n        exam: params.id,\r\n        result: tempResult,\r\n        user: user._id,\r\n      });\r\n\r\n      if (response.success) {\r\n        setView(\"result\");\r\n        window.scrollTo(0, 0);\r\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\r\n\r\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\r\n      dispatch(HideLoading());\r\n\r\n      if (response.success) {\r\n        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));\r\n      } else {\r\n        message.error(response.error || \"Failed to fetch explanation.\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const startTimer = () => {\r\n    const totalSeconds = examData?.duration || 0;\r\n    setSecondsLeft(totalSeconds);\r\n\r\n    const newIntervalId = setInterval(() => {\r\n      setSecondsLeft((prevSeconds) => {\r\n        if (prevSeconds > 0) {\r\n          return prevSeconds - 1;\r\n        } else {\r\n          setTimeUp(true);\r\n          return 0;\r\n        }\r\n      });\r\n    }, 1000);\r\n    setIntervalId(newIntervalId);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (timeUp && view === \"questions\") {\r\n      clearInterval(intervalId);\r\n      calculateResult();\r\n    }\r\n  }, [timeUp, view, intervalId, calculateResult]);\r\n\r\n  useEffect(() => {\r\n    if (params.id) {\r\n      getExamData();\r\n    }\r\n  }, [params.id, getExamData]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      if (intervalId) {\r\n        clearInterval(intervalId);\r\n      }\r\n    };\r\n  }, [intervalId]);\r\n\r\n  // Add fullscreen class for all quiz views (instructions, questions, results)\r\n  useEffect(() => {\r\n    if (view === \"instructions\" || view === \"questions\" || view === \"result\") {\r\n      document.body.classList.add(\"quiz-fullscreen\");\r\n    } else {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    };\r\n  }, [view]);\r\n\r\n  // Repair function for fixing orphaned questions\r\n  const repairExamQuestions = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await fetch('/api/exams/repair-exam-questions', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        },\r\n        body: JSON.stringify({ examId: params.id })\r\n      });\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        message.success(data.message);\r\n        // Reload the exam data\r\n        getExamData();\r\n      } else {\r\n        message.error(data.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(\"Failed to repair exam questions\");\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  // Check if user is authenticated\r\n  if (!user) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\">\r\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\">\r\n          <div className=\"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n            <svg className=\"w-10 h-10 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          </div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Authentication Required</h2>\r\n          <p className=\"text-gray-600 mb-8\">Please log in to access the exam and start your learning journey.</p>\r\n          <button\r\n            className=\"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n            onClick={() => navigate(\"/login\")}\r\n          >\r\n            Go to Login\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return examData ? (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n\r\n      {view === \"instructions\" && (\r\n        <Instructions\r\n          examData={examData}\r\n          setView={setView}\r\n          startTimer={startTimer}\r\n        />\r\n      )}\r\n\r\n      {view === \"questions\" && (\r\n        questions.length === 0 ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\">\r\n                <svg className=\"w-12 h-12 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-amber-800 mb-4\">No Questions Found</h3>\r\n              <p className=\"text-amber-700 mb-8 text-lg leading-relaxed\">\r\n                This exam appears to have no questions. Let's fix this issue and get you started!\r\n              </p>\r\n              <button\r\n                onClick={repairExamQuestions}\r\n                className=\"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n              >\r\n                🔧 Repair Questions\r\n              </button>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\r\n            {/* Modern Quiz Header */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: -20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              className=\"bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50\"\r\n            >\r\n              <div className=\"container-modern\">\r\n                <div className=\"flex items-center justify-between h-16\">\r\n                  {/* Quiz Info */}\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <TbBrain className=\"w-8 h-8 text-primary-600\" />\r\n                    <div>\r\n                      <h1 className=\"text-lg font-semibold text-gray-900\">{examData?.name || \"Quiz\"}</h1>\r\n                      <p className=\"text-sm text-gray-500\">Challenge your brain, Beat the rest</p>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Timer */}\r\n                  <QuizTimer\r\n                    duration={examData?.duration || 0}\r\n                    onTimeUp={() => {\r\n                      setTimeUp(true);\r\n                      calculateResult();\r\n                    }}\r\n                    isActive={!timeUp}\r\n                    showWarning={true}\r\n                    warningThreshold={300}\r\n                  />\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Quiz Content */}\r\n            <div className=\"container-modern py-8\">\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.2 }}\r\n              >\r\n                <QuizQuestion\r\n                  question={{\r\n                    ...questions[selectedQuestionIndex],\r\n                    type: questions[selectedQuestionIndex]?.answerType === \"Options\" ? \"mcq\" :\r\n                          questions[selectedQuestionIndex]?.answerType === \"Free Text\" ||\r\n                          questions[selectedQuestionIndex]?.answerType === \"Fill in the Blank\" ? \"fill\" :\r\n                          questions[selectedQuestionIndex]?.imageUrl ? \"image\" : \"mcq\",\r\n                    options: questions[selectedQuestionIndex]?.options ?\r\n                      Object.values(questions[selectedQuestionIndex].options) : []\r\n                  }}\r\n                  questionNumber={selectedQuestionIndex + 1}\r\n                  totalQuestions={questions.length}\r\n                  selectedAnswer={selectedOptions[selectedQuestionIndex]}\r\n                  onAnswerSelect={(answer) =>\r\n                    setSelectedOptions({\r\n                      ...selectedOptions,\r\n                      [selectedQuestionIndex]: answer,\r\n                    })\r\n                  }\r\n                  onNext={() => {\r\n                    if (selectedQuestionIndex === questions.length - 1) {\r\n                      calculateResult();\r\n                    } else {\r\n                      setSelectedQuestionIndex(selectedQuestionIndex + 1);\r\n                    }\r\n                  }}\r\n                  onPrevious={() => setSelectedQuestionIndex(selectedQuestionIndex - 1)}\r\n                  timeRemaining={secondsLeft}\r\n                  isLastQuestion={selectedQuestionIndex === questions.length - 1}\r\n                />\r\n              </motion.div>\r\n            </div>\r\n\r\n            {/* Timer Overlay for Critical Moments */}\r\n            <QuizTimerOverlay\r\n              timeRemaining={secondsLeft}\r\n              onClose={() => {}}\r\n            />\r\n          </div>\r\n        )\r\n      )}\r\n\r\n      {view === \"result\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\">\r\n          {result.verdict === \"Pass\" && <Confetti width={width} height={height} />}\r\n\r\n          <div className=\"max-w-4xl mx-auto px-4\">\r\n            <div className=\"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\">\r\n              {/* Modern Header */}\r\n              <div className={`px-8 py-10 text-center relative ${\r\n                result.verdict === \"Pass\"\r\n                  ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\"\r\n                  : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"\r\n              }`}>\r\n                <div className=\"relative\">\r\n                  <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500 to-green-600\"\r\n                      : \"bg-gradient-to-br from-amber-500 to-orange-600\"\r\n                  }`}>\r\n                    <img\r\n                      src={result.verdict === \"Pass\" ? Pass : Fail}\r\n                      alt={result.verdict}\r\n                      className=\"w-12 h-12 object-contain\"\r\n                    />\r\n                  </div>\r\n                  <h1 className={`text-4xl font-black mb-4 tracking-tight ${\r\n                    result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"\r\n                  }`}>\r\n                    {result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"}\r\n                  </h1>\r\n                  <p className=\"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\">\r\n                    {result.verdict === \"Pass\"\r\n                      ? \"You've mastered this exam with flying colors!\"\r\n                      : \"Every challenge makes you stronger. Try again!\"}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modern Statistics Cards */}\r\n              <div className=\"p-8\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\r\n                  {/* Score Card */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-blue-600 mb-2 tracking-tight\">\r\n                        {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-blue-700/80 uppercase tracking-wider\">Your Score</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Correct vs Total */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-emerald-600 mb-2 tracking-tight\">\r\n                        {result.correctAnswers?.length || 0}/{questions.length}\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\">Correct</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Pass Status */}\r\n                  <div className={`group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\"\r\n                      : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"\r\n                  }`}>\r\n                    <div className={`absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${\r\n                      result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"\r\n                    } to-transparent`}></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className={`text-4xl font-black mb-2 tracking-tight ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"}\r\n                      </div>\r\n                      <div className={`text-sm font-bold uppercase tracking-wider ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Progress Visualization */}\r\n                <div className=\"mb-8\">\r\n                  <div className=\"relative bg-slate-100 rounded-2xl p-6\">\r\n                    <div className=\"text-center mb-4\">\r\n                      <h3 className=\"text-lg font-bold text-slate-700 mb-1\">Performance Overview</h3>\r\n                      <p className=\"text-sm text-slate-500\">Your achievement level</p>\r\n                    </div>\r\n                    <div className=\"relative\">\r\n                      <div className=\"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\">\r\n                        <div\r\n                          className={`h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${\r\n                            result.verdict === \"Pass\"\r\n                              ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\"\r\n                              : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"\r\n                          }`}\r\n                          style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}\r\n                        >\r\n                          <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"></div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center mt-3\">\r\n                        <span className=\"text-xs font-medium text-slate-500\">0%</span>\r\n                        <span className={`text-lg font-black tracking-tight ${\r\n                          result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                        }`}>\r\n                          {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                        </span>\r\n                        <span className=\"text-xs font-medium text-slate-500\">100%</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Action Buttons */}\r\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n                  <button\r\n                    className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                    onClick={() => setView(\"review\")}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <span className=\"relative\">Review Answers</span>\r\n                  </button>\r\n\r\n                  <button\r\n                    className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                    onClick={() => navigate(\"/user/hub\")}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <span className=\"relative\">🏠 Hub</span>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {view === \"review\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-6\">\r\n          <div className=\"max-w-4xl mx-auto px-4\">\r\n            {/* Simple Header */}\r\n            <div className=\"text-center mb-6\">\r\n              <div className=\"bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-lg border border-slate-200/50\">\r\n                <h2 className=\"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2\">\r\n                  Answer Review\r\n                </h2>\r\n                <p className=\"text-slate-600\">Quick overview of your answers</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Compact Questions Review */}\r\n            <div className=\"space-y-3 mb-6\">\r\n              {questions.map((question, index) => {\r\n                const correctAnswer = question.answerType === \"Options\"\r\n                  ? question.correctOption\r\n                  : question.correctAnswer;\r\n                const isCorrect = correctAnswer === selectedOptions[index];\r\n                const userAnswer = selectedOptions[index];\r\n\r\n                return (\r\n                  <div\r\n                    key={index}\r\n                    className=\"backdrop-blur-md rounded-lg shadow-md border-2 p-4\"\r\n                    style={{\r\n                      backgroundColor: isCorrect ? '#bbf7d0' : '#fecaca',\r\n                      borderColor: isCorrect ? '#22c55e' : '#ef4444'\r\n                    }}\r\n                  >\r\n                    {/* Question */}\r\n                    <div className=\"mb-3\">\r\n                      <div className=\"flex items-start space-x-3\">\r\n                        <div className=\"w-8 h-8 rounded-lg flex items-center justify-center font-bold text-white text-sm bg-blue-600 flex-shrink-0 mt-1\">\r\n                          {index + 1}\r\n                        </div>\r\n                        <div className=\"flex-1\">\r\n                          <p className=\"text-slate-800 font-medium leading-relaxed\">{question.name}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Your Answer with Visual Indicator */}\r\n                    <div className=\"mb-2\">\r\n                      <span className=\"text-sm font-semibold text-slate-600\">Your Answer: </span>\r\n                      <span className={`font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\r\n                        {question.answerType === \"Options\"\r\n                          ? (question.options && userAnswer && question.options[userAnswer]) || userAnswer || \"Not answered\"\r\n                          : userAnswer || \"Not answered\"}\r\n                      </span>\r\n                      {isCorrect ? (\r\n                        <span className=\"ml-3 text-green-600 text-2xl font-black\">✓</span>\r\n                      ) : (\r\n                        <span className=\"ml-3 text-red-600 text-2xl font-black\">✗</span>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* Correct Answer (only for wrong answers) */}\r\n                    {!isCorrect && (\r\n                      <div className=\"mb-2\">\r\n                        <span className=\"text-sm font-semibold text-slate-600\">Correct Answer: </span>\r\n                        <span className=\"font-medium text-green-700\">\r\n                          {question.answerType === \"Options\"\r\n                            ? (question.options && question.options[question.correctOption]) || question.correctOption\r\n                            : (question.correctAnswer || question.correctOption)}\r\n                        </span>\r\n                        <span className=\"ml-3 text-green-500 text-2xl font-black\">✓</span>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* See Explanation Button (only for wrong answers) */}\r\n                    {!isCorrect && (\r\n                      <div className=\"mt-2\">\r\n                        <button\r\n                          className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2\"\r\n                          onClick={() => {\r\n                            console.log('Fetching explanation for:', question.name);\r\n                            fetchExplanation(\r\n                              question.name,\r\n                              question.answerType === \"Options\"\r\n                                ? (question.options && question.options[question.correctOption]) || question.correctOption\r\n                                : (question.correctAnswer || question.correctOption),\r\n                              question.answerType === \"Options\"\r\n                                ? (question.options && question.options[userAnswer]) || userAnswer || \"Not answered\"\r\n                                : userAnswer || \"Not answered\",\r\n                              question.image\r\n                            );\r\n                          }}\r\n                        >\r\n                          <span>💡</span>\r\n                          <span>Get Explanation</span>\r\n                        </button>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* Explanation */}\r\n                    {explanations[question.name] && (\r\n                      <div className=\"mt-2 p-3 bg-white rounded-lg border-l-4 border-l-blue-500 shadow-sm border border-gray-200\">\r\n                        <div className=\"flex items-center mb-2\">\r\n                          <span className=\"text-blue-600 text-lg mr-2\">💡</span>\r\n                          <h6 className=\"font-bold text-gray-800 text-base\">\r\n                            Explanation\r\n                          </h6>\r\n                        </div>\r\n\r\n                        {/* Show diagram/image for image-based questions */}\r\n                        {(question.image || question.imageUrl) && (\r\n                          <div className=\"mb-3 p-2 bg-gray-50 rounded border border-gray-200\">\r\n                            <div className=\"flex items-center mb-1\">\r\n                              <span className=\"text-gray-700 text-sm font-medium\">📊 Reference Diagram:</span>\r\n                            </div>\r\n                            <div className=\"flex justify-center\">\r\n                              <img\r\n                                src={question.image || question.imageUrl}\r\n                                alt=\"Question diagram\"\r\n                                className=\"max-w-full max-h-48 object-contain rounded border border-gray-300\"\r\n                                style={{ maxWidth: '350px' }}\r\n                              />\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n\r\n                        <div className=\"text-sm text-gray-800 leading-relaxed bg-gray-50 p-2 rounded\">\r\n                          <ContentRenderer text={explanations[question.name]} />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n\r\n            {/* Modern Navigation */}\r\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\r\n              <button\r\n                className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                onClick={() => setView(\"result\")}\r\n              >\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                <span className=\"relative\">← Back to Results</span>\r\n              </button>\r\n\r\n              <button\r\n                className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                onClick={() => {\r\n                  // Reset exam state and restart\r\n                  setView(\"instructions\");\r\n                  setSelectedQuestionIndex(0);\r\n                  setSelectedOptions({});\r\n                  setResult({});\r\n                  setTimeUp(false);\r\n                  setSecondsLeft(examData?.duration || 0);\r\n                  setExplanations({});\r\n                }}\r\n              >\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                <span className=\"relative\">🔄 Retake Quiz</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  ) : null;\r\n}\r\n\r\nexport default WriteExam;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\index.js", ["467", "468", "469", "470", "471", "472", "473", "474", "475", "476", "477", "478", "479", "480", "481", "482", "483"], [], "import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from \"react\";\nimport \"./index.css\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getStudyMaterial } from \"../../../apicalls/study\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\n// import { Card, Button, Input, Loading } from \"../../../components/modern\";\n\nimport PDFModal from \"./PDFModal\";\nimport {\n  FaPlayCircle,\n  FaBook,\n  FaVideo,\n  FaFileAlt,\n  FaGraduationCap,\n  FaDownload,\n  FaEye,\n  FaTimes,\n  FaChevronDown,\n  FaSearch,\n  FaExpand,\n  FaCompress,\n} from \"react-icons/fa\";\nimport {\n  TbVideo,\n  TbFileText,\n  TbBook as TbBookIcon,\n  TbScho<PERSON>,\n  Tb<PERSON><PERSON><PERSON>,\n  <PERSON>b<PERSON><PERSON><PERSON>,\n  Tb<PERSON>ortAscending,\n  Tb<PERSON><PERSON>,\n  TbD<PERSON>load,\n  Tb<PERSON><PERSON>,\n  TbCalendar,\n  Tb<PERSON>ser,\n  TbChevronDown as TbChevronDownIcon,\n  TbChevronUp,\n  TbX,\n  TbAlertTriangle,\n  TbInfoCircle,\n  TbCheck,\n  TbSubtitles,\n  TbBooks,\n  TbCertificate\n} from \"react-icons/tb\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects.jsx\";\n\nfunction StudyMaterial() {\n  const { user } = useSelector((state) => state.user);\n  const dispatch = useDispatch();\n\n  // Get user level and subjects list (case-insensitive)\n  const userLevel = user?.level || 'Primary';\n  const userLevelLower = userLevel.toLowerCase();\n  const subjectsList = userLevelLower === 'primary'\n    ? primarySubjects\n    : userLevelLower === 'secondary'\n      ? secondarySubjects\n      : advanceSubjects;\n\n  // Debug: Log current level and subjects\n  useEffect(() => {\n    console.log('📚 Study Materials - User Level:', userLevel);\n    console.log('📚 Study Materials - User Level (lowercase):', userLevelLower);\n    console.log('📚 Study Materials - Subjects List:', subjectsList);\n    console.log('📚 Study Materials - User Data:', user);\n  }, [userLevel, userLevelLower, subjectsList, user]);\n\n  // Define all possible classes for each level\n  const allPossibleClasses = userLevelLower === 'primary'\n    ? ['1', '2', '3', '4', '5', '6', '7']\n    : userLevelLower === 'secondary'\n      ? ['Form-1', 'Form-2', 'Form-3', 'Form-4']\n      : ['Form-5', 'Form-6'];\n\n  // Simplified state management - initialize with user's class if available\n  const [activeTab, setActiveTab] = useState(\"videos\");\n  const [selectedClass, setSelectedClass] = useState(user?.class || user?.className || \"all\");\n  const [selectedSubject, setSelectedSubject] = useState(\"all\");\n\n  // Get user's current class for highlighting\n  const userCurrentClass = user?.class || user?.className;\n  const [materials, setMaterials] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const [showVideoIndices, setShowVideoIndices] = useState([]);\n  const [modalIsOpen, setModalIsOpen] = useState(false);\n  const [documentUrl, setDocumentUrl] = useState(\"\");\n  const [availableClasses, setAvailableClasses] = useState([]);\n  const [showClassSelector, setShowClassSelector] = useState(false);\n  const [isVideoExpanded, setIsVideoExpanded] = useState(false);\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);\n  const [videoError, setVideoError] = useState(null);\n  const [selectedSubtitle, setSelectedSubtitle] = useState('off');\n  const [videoRef, setVideoRef] = useState(null);\n\n\n  // Unified search and sort states\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"newest\");\n\n  // Update selectedClass when user data becomes available\n  useEffect(() => {\n    const userClass = user?.class || user?.className;\n    if (userClass && selectedClass === \"all\" && !availableClasses.length) {\n      setSelectedClass(userClass);\n    }\n  }, [user, selectedClass, availableClasses.length]);\n\n  // Reset subject selection when user level changes\n  useEffect(() => {\n    if (user?.level) {\n      // Check if current selected subject is valid for the new level\n      const isValidSubject = subjectsList.includes(selectedSubject);\n      if (!isValidSubject && selectedSubject !== \"all\") {\n        console.log('📚 Resetting subject selection due to level change');\n        setSelectedSubject(\"all\");\n      }\n    }\n  }, [user?.level, subjectsList, selectedSubject]);\n\n  // Set available classes based on user level (show all possible classes)\n  const setAvailableClassesForLevel = useCallback(() => {\n    setAvailableClasses(allPossibleClasses);\n  }, [allPossibleClasses]);\n\n  // Simplified fetch function\n  const fetchMaterials = useCallback(async () => {\n    if (!activeTab || selectedClass === \"default\") {\n      return;\n    }\n\n    setIsLoading(true);\n    setError(null);\n    dispatch(ShowLoading());\n\n    try {\n      // Normalize className for backend - remove \"Form-\" prefix if present\n      const normalizedClassName = selectedClass === \"all\" ? \"all\" :\n        selectedClass.toString().replace(\"Form-\", \"\");\n\n      const data = {\n        content: activeTab,\n        className: normalizedClassName,\n        subject: selectedSubject, // This can be \"all\" or a specific subject\n      };\n      if (userLevel) {\n        data.level = userLevel;\n      }\n\n      const res = await getStudyMaterial(data);\n\n      if (res.status === 200 && res.data.success) {\n        const materials = res.data.data === \"empty\" ? [] : res.data.data;\n        setMaterials(materials);\n      } else {\n        setMaterials([]);\n        setError(`Failed to fetch ${activeTab}. Please try again.`);\n      }\n    } catch (error) {\n      console.error(\"Error fetching study material:\", error);\n      setMaterials([]);\n      setError(`Unable to load ${activeTab}. Please check your connection and try again.`);\n    } finally {\n      setIsLoading(false);\n      dispatch(HideLoading());\n    }\n  }, [activeTab, selectedClass, selectedSubject, userLevel, dispatch]);\n\n  // Set available classes when component mounts\n  useEffect(() => {\n    if (user && userLevel) {\n      setAvailableClassesForLevel();\n    }\n  }, [user, userLevel, setAvailableClassesForLevel]);\n\n  // Fetch materials when filters change or component mounts\n  useEffect(() => {\n    // Only fetch if we have a valid activeTab, selectedClass, and user\n    if (user && userLevel && activeTab && selectedClass && selectedClass !== \"default\") {\n      fetchMaterials();\n    }\n  }, [user, userLevel, activeTab, selectedClass, selectedSubject, fetchMaterials]);\n\n  // Handler functions\n  const handleTabChange = (tab) => {\n    setMaterials([]);\n    setActiveTab(tab);\n    setSearchTerm(\"\");\n    setSortBy(\"newest\");\n  };\n\n  const handleSubjectChange = (subject) => {\n    setMaterials([]);\n    setSelectedSubject(subject);\n    setSearchTerm(\"\");\n  };\n\n  const handleClassChange = (className) => {\n    setMaterials([]);\n    setSelectedClass(className);\n    setShowClassSelector(false);\n  };\n\n  const toggleClassSelector = () => {\n    setShowClassSelector(!showClassSelector);\n  };\n\n  // Unified filtering and sorting logic\n  const filteredAndSortedMaterials = useMemo(() => {\n    if (!materials || materials.length === 0) {\n      return [];\n    }\n\n    let filtered = materials;\n\n    // Filter by search term (title, subject, or year)\n    if (searchTerm.trim()) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(material =>\n        material.title.toLowerCase().includes(searchLower) ||\n        material.subject.toLowerCase().includes(searchLower) ||\n        (material.year && material.year.toLowerCase().includes(searchLower))\n      );\n    }\n\n    // Sort by year, creation date, or title\n    filtered.sort((a, b) => {\n      if (sortBy === \"newest\") {\n        // For materials with year field (books, past papers)\n        if (a.year && b.year) {\n          return parseInt(b.year) - parseInt(a.year);\n        }\n        // For videos (no year field), sort by creation date or reverse order\n        else if (activeTab === \"videos\") {\n          // Since videos are fetched in reverse order from server, maintain that for \"newest\"\n          return 0; // Keep original order (newest first from server)\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else if (sortBy === \"oldest\") {\n        // For materials with year field\n        if (a.year && b.year) {\n          return parseInt(a.year) - parseInt(b.year);\n        }\n        // For videos, reverse the order\n        else if (activeTab === \"videos\") {\n          return 0; // Will be reversed after sort\n        }\n        // Fallback: materials with year come first\n        else if (a.year && !b.year) return -1;\n        else if (!a.year && b.year) return 1;\n        else return 0;\n      } else {\n        // Sort by title alphabetically\n        return a.title.localeCompare(b.title);\n      }\n    });\n\n    // For videos with \"oldest\" sort, reverse the array\n    if (activeTab === \"videos\" && sortBy === \"oldest\") {\n      filtered.reverse();\n    }\n\n    return filtered;\n  }, [materials, searchTerm, sortBy, activeTab]);\n\n  // Document handlers\n  const handleDocumentDownload = (documentUrl) => {\n    fetch(documentUrl)\n      .then((response) => response.blob())\n      .then((blob) => {\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = documentUrl.split(\"/\").pop();\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n      })\n      .catch((error) => {\n        console.error(\"Error downloading the file:\", error);\n      });\n  };\n\n  const handleDocumentPreview = (documentUrl) => {\n    setDocumentUrl(documentUrl);\n    setModalIsOpen(true);\n  };\n\n  // Video handlers\n  const handleShowVideo = async (index) => {\n    const video = filteredAndSortedMaterials[index];\n\n    setCurrentVideoIndex(index);\n    setShowVideoIndices([index]);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n\n    // Get signed URL for S3 videos if needed\n    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {\n      try {\n        const signedUrl = await getSignedVideoUrl(video.videoUrl);\n        video.signedVideoUrl = signedUrl;\n      } catch (error) {\n        console.warn('Failed to get signed URL, using original URL');\n        video.signedVideoUrl = video.videoUrl;\n      }\n    }\n  };\n\n  const handleHideVideo = () => {\n    setShowVideoIndices([]);\n    setCurrentVideoIndex(null);\n    setIsVideoExpanded(false);\n    setVideoError(null);\n    setSelectedSubtitle('off');\n    setVideoRef(null);\n  };\n\n  // Handle subtitle selection\n  const handleSubtitleChange = (language) => {\n    setSelectedSubtitle(language);\n\n    if (videoRef) {\n      const tracks = videoRef.textTracks;\n\n      // Disable all tracks first\n      for (let i = 0; i < tracks.length; i++) {\n        tracks[i].mode = 'disabled';\n      }\n\n      // Enable selected track\n      if (language !== 'off') {\n        for (let i = 0; i < tracks.length; i++) {\n          if (tracks[i].language === language) {\n            tracks[i].mode = 'showing';\n            break;\n          }\n        }\n      }\n    }\n  };\n\n  const handleExpandVideo = () => {\n    setIsVideoExpanded(true);\n  };\n\n  const handleCollapseVideo = () => {\n    setIsVideoExpanded(false);\n  };\n\n\n\n\n\n\n\n\n\n  // Note: Auto-refresh removed since videos are now uploaded synchronously\n\n  // Get signed URL for S3 videos to ensure access\n  const getSignedVideoUrl = async (videoUrl) => {\n    if (!videoUrl) return videoUrl;\n\n    // For AWS S3 URLs, get signed URL from backend\n    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {\n      try {\n        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {\n          method: 'GET',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          credentials: 'include'\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.signedUrl) {\n          console.log('✅ Got signed URL for S3 video');\n          return data.signedUrl;\n        } else {\n          console.warn('⚠️ Invalid response from signed URL endpoint:', data);\n          return videoUrl;\n        }\n      } catch (error) {\n        console.warn('⚠️ Failed to get signed URL, using original:', error.message);\n        return videoUrl;\n      }\n    }\n\n    // For other URLs or if signed URL fails, return as-is\n    return videoUrl;\n  };\n\n\n\n\n\n  // Get appropriate thumbnail URL for video\n  const getThumbnailUrl = (material) => {\n    // If we have a custom thumbnail, use it\n    if (material.thumbnail && material.thumbnail !== \"\" && material.thumbnail !== \"processing\") {\n      return material.thumbnail;\n    }\n\n    // For YouTube videos, extract video ID and use YouTube thumbnail\n    if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n      // Extract YouTube video ID if it's a full URL\n      let videoId = material.videoID;\n      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n        const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n        videoId = match ? match[1] : videoId;\n      }\n      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n    }\n\n    // For uploaded videos without thumbnails, use a default placeholder\n    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n  };\n\n  // Keyboard support for video modal\n  useEffect(() => {\n    const handleKeyPress = (event) => {\n      if (showVideoIndices.length > 0) {\n        switch (event.key) {\n          case 'Escape':\n            handleHideVideo();\n            break;\n          case 'f':\n          case 'F':\n            if (!isVideoExpanded) {\n              handleExpandVideo();\n            } else {\n              handleCollapseVideo();\n            }\n            break;\n          default:\n            break;\n        }\n      }\n    };\n\n    document.addEventListener('keydown', handleKeyPress);\n    return () => {\n      document.removeEventListener('keydown', handleKeyPress);\n    };\n  }, [showVideoIndices, isVideoExpanded]);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\n      {/* Modern Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"bg-gradient-to-r from-primary-600 to-blue-600 text-white\"\n      >\n        <div className=\"container-modern py-12\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-6\">\n              <div className=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\">\n                <TbBooks className=\"w-8 h-8 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-4xl font-bold mb-2\">Study Materials</h1>\n                <p className=\"text-xl text-blue-100\">\n                  Access comprehensive learning resources for {userLevel} education\n                </p>\n              </div>\n            </div>\n            <div className=\"hidden md:block\">\n              <div className=\"bg-white/20 backdrop-blur-sm rounded-xl px-6 py-3\">\n                <div className=\"text-sm text-blue-100 mb-1\">Current Level</div>\n                <div className=\"text-lg font-bold\">{userLevel?.toUpperCase()}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      <div className=\"container-modern py-8\">\n        {/* Study Material Tabs */}\n        <div className=\"mb-6\">\n          <div className=\"study-tabs\">\n            {[\n              { key: 'videos', label: 'Videos', icon: TbVideo },\n              { key: 'study-notes', label: 'Notes', icon: TbFileText },\n              { key: 'past-papers', label: 'Past Papers', icon: TbCertificate },\n              { key: 'books', label: 'Books', icon: TbBookIcon }\n            ].map((tab) => (\n              <button\n                key={tab.key}\n                className={`study-tab ${activeTab === tab.key ? 'active' : ''}`}\n                onClick={() => handleTabChange(tab.key)}\n              >\n                <tab.icon />\n                <span>{tab.label}</span>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Modern Filters Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4 }}\n          className=\"mb-8\"\n        >\n          <div className=\"card p-6\">\n            <div className=\"flex flex-col lg:flex-row gap-6 items-end\">\n              {/* Search */}\n              <div className=\"flex-1\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Search Materials\n                </label>\n                <input\n                  placeholder={`Search ${activeTab.replace('-', ' ')}...`}\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"form-input\"\n                />\n              </div>\n\n              {/* Class Filter */}\n              <div className=\"w-full lg:w-64\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Filter by Class\n                  {userCurrentClass && (\n                    <span className=\"ml-2 text-xs text-primary-600 font-medium\">\n                      (Your class: {userLevelLower === 'primary' ? `Class ${userCurrentClass}` : `Form ${userCurrentClass}`})\n                    </span>\n                  )}\n                </label>\n                <div className=\"relative\">\n                  <button\n                    onClick={toggleClassSelector}\n                    className=\"w-full input-modern flex items-center justify-between\"\n                  >\n                    <span className=\"flex items-center space-x-2\">\n                      <TbSchool className=\"w-4 h-4 text-gray-400\" />\n                      <span>\n                        {selectedClass === 'all' ? 'All Classes' :\n                          userLevelLower === 'primary'\n                            ? `Class ${selectedClass}`\n                            : `Form ${selectedClass}`\n                        }\n                      </span>\n                      {selectedClass === userCurrentClass && (\n                        <span className=\"badge-primary text-xs\">Current</span>\n                      )}\n                    </span>\n                    <TbChevronDownIcon className={`w-4 h-4 text-gray-400 transition-transform ${showClassSelector ? 'rotate-180' : ''}`} />\n                  </button>\n\n                  <AnimatePresence>\n                    {showClassSelector && (\n                      <motion.div\n                        initial={{ opacity: 0, y: -10 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        exit={{ opacity: 0, y: -10 }}\n                        className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto\"\n                      >\n                        <button\n                          className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors ${\n                            selectedClass === 'all' ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'\n                          }`}\n                          onClick={() => handleClassChange('all')}\n                        >\n                          All Classes\n                        </button>\n                        {availableClasses.map((className, index) => (\n                          <button\n                            key={index}\n                            className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center justify-between ${\n                              selectedClass === className ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'\n                            }`}\n                            onClick={() => handleClassChange(className)}\n                          >\n                            <span>\n                              {userLevelLower === 'primary' ? `Class ${className}` : `Form ${className}`}\n                            </span>\n                            {className === userCurrentClass && (\n                              <span className=\"badge-success text-xs\">Your Class</span>\n                            )}\n                          </button>\n                        ))}\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n                </div>\n              </div>\n\n              {/* Subject Filter */}\n              <div className=\"w-full lg:w-64\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Filter by Subject\n                </label>\n                <select\n                  value={selectedSubject}\n                  onChange={(e) => handleSubjectChange(e.target.value)}\n                  className=\"input-modern\"\n                >\n                  <option value=\"all\">All Subjects</option>\n                  {subjectsList.map((subject, index) => (\n                    <option key={index} value={subject}>\n                      {subject}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Sort */}\n              <div className=\"w-full lg:w-48\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Sort by\n                </label>\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  className=\"input-modern\"\n                >\n                  <option value=\"newest\">Newest First</option>\n                  <option value=\"oldest\">Oldest First</option>\n                  <option value=\"title\">By Title</option>\n                </select>\n              </div>\n\n              {/* Clear Filters */}\n              <button\n                className=\"btn btn-secondary\"\n                onClick={() => {\n                  setSearchTerm(\"\");\n                  setSelectedClass(\"all\");\n                  setSelectedSubject(\"all\");\n                  setSortBy(\"newest\");\n                }}\n              >\n                Clear Filters\n              </button>\n            </div>\n\n            {/* Results Count */}\n            {(searchTerm || selectedClass !== \"all\" || selectedSubject !== \"all\") && (\n              <div className=\"mt-4 pt-4 border-t border-gray-100\">\n                <span className=\"text-sm text-gray-600\">\n                  Showing {filteredAndSortedMaterials.length} of {materials.length} {activeTab.replace('-', ' ')}\n                </span>\n              </div>\n            )}\n          </div>\n        </motion.div>\n\n      {/* Materials Display */}\n      <div className=\"materials-section\">\n        {isLoading ? (\n          <div className=\"loading-state\">\n            <div className=\"loading-spinner\"></div>\n            <p>Loading materials...</p>\n          </div>\n        ) : error ? (\n          <div className=\"error-state\">\n            <FaTimes className=\"error-icon\" />\n            <h3>Error Loading Materials</h3>\n            <p>{error}</p>\n            <button\n              className=\"retry-btn\"\n              onClick={() => {\n                setError(null);\n                fetchMaterials();\n              }}\n            >\n              Try Again\n            </button>\n          </div>\n        ) : filteredAndSortedMaterials.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {filteredAndSortedMaterials.map((material, index) => (\n              <div key={index} className=\"study-card\">\n                <div className=\"study-card-header\">\n                  <div className=\"study-card-meta\">\n                    {activeTab === 'videos' && <FaVideo />}\n                    {activeTab === 'study-notes' && <FaFileAlt />}\n                    {activeTab === 'past-papers' && <FaFileAlt />}\n                    {activeTab === 'books' && <FaBook />}\n                    <span>\n                      {activeTab === 'study-notes' ? 'Note' :\n                       activeTab === 'past-papers' ? 'Past Paper' :\n                       activeTab === 'videos' ? 'Video' : 'Book'}\n                    </span>\n                  </div>\n\n                  <div className=\"study-card-title\">\n                    {material.title}\n                  </div>\n\n                  {/* Class tags for videos */}\n                  {activeTab === 'videos' && material.coreClass && (\n                    <div className=\"d-flex gap-2 mt-2\">\n                      {material.isCore ? (\n                        <span className=\"badge badge-primary\">\n                          Core Class {userLevelLower === 'primary' ? material.coreClass : `Form ${material.coreClass}`}\n                        </span>\n                      ) : material.sharedFromClass && (\n                        <span className=\"badge badge-secondary\">\n                          Shared from {userLevelLower === 'primary' ? `Class ${material.sharedFromClass}` : `Form ${material.sharedFromClass}`}\n                        </span>\n                      )}\n                    </div>\n                  )}\n                  {material.year && (\n                    <span className=\"badge badge-secondary mt-2\">{material.year}</span>\n                  )}\n                </div>\n\n                {/* Video Thumbnail for videos */}\n                {activeTab === 'videos' && (material.videoUrl || material.videoID) && (\n                  <div className=\"video-thumbnail-container\" onClick={() => handleShowVideo(index)}>\n                    <img\n                      src={getThumbnailUrl(material)}\n                      alt={material.title}\n                      className=\"video-thumbnail\"\n                      onError={(e) => {\n                        // Fallback logic for failed thumbnails\n                        if (material.videoID && !material.videoID.includes('amazonaws.com')) {\n                          // For YouTube videos, try different quality thumbnails\n                          let videoId = material.videoID;\n                          if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {\n                            const match = videoId.match(/(?:youtube\\.com\\/watch\\?v=|youtu\\.be\\/)([^&\\n?#]+)/);\n                            videoId = match ? match[1] : videoId;\n                          }\n\n                          if (!e.target.src.includes('youtube.com')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;\n                          } else if (e.target.src.includes('maxresdefault')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;\n                          } else if (e.target.src.includes('mqdefault')) {\n                            e.target.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;\n                          } else {\n                            // Final fallback to default placeholder\n                            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                          }\n                        } else {\n                          // For uploaded videos without thumbnails, use default placeholder\n                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';\n                        }\n                      }}\n                    />\n                    <div className=\"play-overlay\">\n                      <FaPlayCircle className=\"play-icon\" />\n                      <span className=\"play-text\">Watch Now</span>\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"card-content\">\n                  <h3 className=\"material-title\">{material.title}</h3>\n                  <div className=\"material-meta\">\n                    <span className=\"material-subject\">{material.subject}</span>\n                    {material.className && (\n                      <span className=\"material-class\">\n                        {userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`}\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                <div className=\"card-actions\">\n                  {activeTab === 'videos' && (material.videoUrl || material.videoID) ? (\n                    <div className=\"video-info-text\">\n                      <span className=\"video-duration\">Click thumbnail to play</span>\n                    </div>\n                  ) : material.documentUrl ? (\n                    <>\n                      <button\n                        className=\"action-btn secondary\"\n                        onClick={() => handleDocumentPreview(material.documentUrl)}\n                      >\n                        <FaEye /> View\n                      </button>\n                      <button\n                        className=\"action-btn primary\"\n                        onClick={() => handleDocumentDownload(material.documentUrl)}\n                      >\n                        <FaDownload /> Download\n                      </button>\n                    </>\n                  ) : (\n                    <span className=\"unavailable\">Not available</span>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"empty-state\">\n            <FaGraduationCap className=\"empty-icon\" />\n            <h3>No Materials Found</h3>\n            <p>No study materials are available for your current selection.</p>\n            <p className=\"suggestion\">Try selecting a different class or subject.</p>\n          </div>\n        )}\n      </div>\n\n      {/* Enhanced Video Display */}\n      {showVideoIndices.length > 0 && currentVideoIndex !== null && (\n        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {\n          if (e.target === e.currentTarget) handleHideVideo();\n        }}>\n          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>\n            {(() => {\n              const video = filteredAndSortedMaterials[currentVideoIndex];\n              if (!video) return <div>Video not found</div>;\n\n              return (\n                <>\n                  <div className=\"video-header\">\n                    <div className=\"video-info\">\n                      <h3>{video.title || 'Untitled Video'}</h3>\n                      <div className=\"video-meta\">\n                        <span className=\"video-subject\">{video.subject || 'Unknown Subject'}</span>\n                        <span className=\"video-class\">Class {video.className || 'N/A'}</span>\n                      </div>\n                    </div>\n                    <div className=\"video-controls\">\n                      {!isVideoExpanded ? (\n                        <button\n                          className=\"control-btn expand-btn\"\n                          onClick={handleExpandVideo}\n                          title=\"Expand to fullscreen\"\n                        >\n                          <FaExpand />\n                        </button>\n                      ) : (\n                        <button\n                          className=\"control-btn collapse-btn\"\n                          onClick={handleCollapseVideo}\n                          title=\"Exit fullscreen\"\n                        >\n                          <FaCompress />\n                        </button>\n                      )}\n                      <button\n                        className=\"control-btn close-btn\"\n                        onClick={handleHideVideo}\n                        title=\"Close video\"\n                      >\n                        <FaTimes />\n                      </button>\n                    </div>\n                  </div>\n                  <div className=\"video-container\">\n                    {video.videoUrl ? (\n                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>\n                          <video\n                            ref={(ref) => setVideoRef(ref)}\n                            controls\n                            autoPlay\n                            playsInline\n                            preload=\"metadata\"\n                            width=\"100%\"\n                            height=\"400\"\n                            poster={getThumbnailUrl(video)}\n                            style={{\n                              width: '100%',\n                              height: '400px',\n                              backgroundColor: '#000'\n                            }}\n                            onError={(e) => {\n                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);\n                            }}\n                            onCanPlay={() => {\n                              setVideoError(null);\n                            }}\n                            onLoadedMetadata={() => {\n                              // Auto-enable first subtitle if available and none selected\n                              if (video.subtitles && video.subtitles.length > 0 && selectedSubtitle === 'off') {\n                                const defaultSubtitle = video.subtitles.find(sub => sub.isDefault) || video.subtitles[0];\n                                handleSubtitleChange(defaultSubtitle.language);\n                              }\n                            }}\n                            crossOrigin=\"anonymous\"\n                          >\n                            {/* Use signed URL if available, otherwise use original URL */}\n                            <source src={video.signedVideoUrl || video.videoUrl} type=\"video/mp4\" />\n\n                            {/* Add subtitle tracks if available */}\n                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (\n                              <track\n                                key={`${subtitle.language}-${index}`}\n                                kind=\"subtitles\"\n                                src={subtitle.url}\n                                srcLang={subtitle.language}\n                                label={subtitle.languageName}\n                                default={subtitle.isDefault || index === 0}\n                              />\n                            ))}\n\n                            <p style={{color: 'white', textAlign: 'center', padding: '20px'}}>\n                              Your browser does not support the video tag.\n                              <br />\n                              <a href={video.signedVideoUrl || video.videoUrl} target=\"_blank\" rel=\"noopener noreferrer\" style={{color: '#4fc3f7'}}>\n                                Click here to open video in new tab\n                              </a>\n                            </p>\n                          </video>\n\n                          {/* Custom Subtitle Controls */}\n                          {video.subtitles && video.subtitles.length > 0 && (\n                            <div style={{\n                              padding: '12px 15px',\n                              background: 'rgba(0,0,0,0.8)',\n                              borderRadius: '0 0 8px 8px',\n                              borderTop: '1px solid #333'\n                            }}>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '12px',\n                                flexWrap: 'wrap'\n                              }}>\n                                <span style={{\n                                  color: '#fff',\n                                  fontSize: '14px',\n                                  fontWeight: '500',\n                                  minWidth: 'fit-content'\n                                }}>\n                                  📝 Choose Language:\n                                </span>\n\n                                <div style={{\n                                  display: 'flex',\n                                  gap: '8px',\n                                  flexWrap: 'wrap',\n                                  flex: 1\n                                }}>\n                                  {/* Off Button */}\n                                  <button\n                                    onClick={() => handleSubtitleChange('off')}\n                                    style={{\n                                      padding: '6px 12px',\n                                      borderRadius: '20px',\n                                      border: 'none',\n                                      fontSize: '12px',\n                                      fontWeight: '500',\n                                      cursor: 'pointer',\n                                      transition: 'all 0.2s ease',\n                                      backgroundColor: selectedSubtitle === 'off' ? '#ff4757' : '#2f3542',\n                                      color: '#fff'\n                                    }}\n                                  >\n                                    OFF\n                                  </button>\n\n                                  {/* Language Buttons */}\n                                  {video.subtitles.map((subtitle) => (\n                                    <button\n                                      key={subtitle.language}\n                                      onClick={() => handleSubtitleChange(subtitle.language)}\n                                      style={{\n                                        padding: '6px 12px',\n                                        borderRadius: '20px',\n                                        border: 'none',\n                                        fontSize: '12px',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        transition: 'all 0.2s ease',\n                                        backgroundColor: selectedSubtitle === subtitle.language ? '#2ed573' : '#57606f',\n                                        color: '#fff',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '4px'\n                                      }}\n                                    >\n                                      <span>{subtitle.languageName}</span>\n                                      {subtitle.isAutoGenerated && (\n                                        <span style={{\n                                          fontSize: '10px',\n                                          opacity: 0.8,\n                                          backgroundColor: 'rgba(255,255,255,0.2)',\n                                          padding: '1px 4px',\n                                          borderRadius: '8px'\n                                        }}>\n                                          AI\n                                        </span>\n                                      )}\n                                    </button>\n                                  ))}\n                                </div>\n                              </div>\n                            </div>\n                          )}\n                        </div>\n                    ) : video.videoID ? (\n                      // Fallback to YouTube embed if no videoUrl\n                      <iframe\n                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}\n                        title={video.title}\n                        frameBorder=\"0\"\n                        allowFullScreen\n                        className=\"video-iframe\"\n                        onLoad={() => console.log('✅ YouTube iframe loaded')}\n                      ></iframe>\n                    ) : (\n                      <div className=\"video-error\">\n                        <div className=\"error-icon\">⚠️</div>\n                        <h3>Video Unavailable</h3>\n                        <p>{videoError || \"This video cannot be played at the moment.\"}</p>\n                        <div className=\"error-actions\">\n                          <a\n                            href={video.signedVideoUrl || video.videoUrl}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"external-link-btn\"\n                          >\n                            📱 Open in New Tab\n                          </a>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                  {!isVideoExpanded && (\n                    <div className=\"video-footer\">\n                      <div className=\"video-description\">\n                        <p>Watch this educational video to learn more about {video.subject}.</p>\n                        {video.subtitleGenerationStatus === 'processing' && (\n                          <div className=\"subtitle-status\" style={{\n                            marginTop: '10px',\n                            fontSize: '0.9em',\n                            color: '#2196F3',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '8px'\n                          }}>\n                            <div style={{\n                              width: '16px',\n                              height: '16px',\n                              border: '2px solid #2196F3',\n                              borderTop: '2px solid transparent',\n                              borderRadius: '50%',\n                              animation: 'spin 1s linear infinite'\n                            }}></div>\n                            🤖 Generating subtitles...\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )}\n                </>\n              );\n            })()}\n          </div>\n        </div>\n      )}\n\n      {/* PDF Modal */}\n      <PDFModal\n        modalIsOpen={modalIsOpen}\n        closeModal={() => {\n          setModalIsOpen(false);\n          setDocumentUrl(\"\");\n        }}\n        documentUrl={documentUrl}\n      />\n      </div>\n    </div>\n  );\n}\n\nexport default StudyMaterial;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\UserReports\\index.js", ["484", "485"], [], "import React from \"react\";\r\nimport './index.css';\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Modal, Table } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\r\nimport { useEffect } from \"react\";\r\nimport moment from \"moment\";\r\n\r\nfunction UserReports() {\r\n  const [reportsData, setReportsData] = React.useState([]);\r\n  const dispatch = useDispatch();\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"examName\",\r\n      render: (text, record) => <>{record.exam?.name}</>,\r\n    },\r\n    {\r\n      title: \"Date\",\r\n      dataIndex: \"date\",\r\n      render: (text, record) => (\r\n        <>{moment(record.createdAt).format(\"DD-MM-YYYY hh:mm:ss\")}</>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Total Marks\",\r\n      dataIndex: \"totalQuestions\",\r\n      render: (text, record) => <>{record.exam?.totalMarks}</>,\r\n    },\r\n    {\r\n      title: \"Passing Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.exam?.passingMarks}</>,\r\n    },\r\n    {\r\n      title: \"Obtained Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.result?.correctAnswers.length}</>,\r\n    },\r\n    {\r\n      title: \"Verdict\",\r\n      dataIndex: \"verdict\",\r\n      render: (text, record) => <>{record.result?.verdict}</>,\r\n    },\r\n  ];\r\n\r\n  const getData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"reports-container\">\r\n      <PageTitle title=\"Reports\" />\r\n      <div className=\"divider\"></div>\r\n      <Table \r\n      columns={columns} \r\n      dataSource={reportsData} \r\n      rowKey={(record) => record._id} \r\n      scroll={{ x: true }} \r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default UserReports;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\index.js", ["486", "487", "488", "489", "490", "491", "492", "493"], [], "import React, { useState, useEffect } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { motion } from \"framer-motion\";\r\nimport { message } from \"antd\";\r\nimport { getAllExams } from \"../../../apicalls/exams\";\r\nimport { getAllReportsByUser } from \"../../../apicalls/reports\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport Select from \"react-select\";\r\nimport { QuizGrid, Card, Button, Input, Loading } from \"../../../components/modern\";\r\nimport { TbSearch, TbFilter, TbTrophy, TbClock, TbUsers, TbQuestionMark, TbBrain } from \"react-icons/tb\";\r\nimport { BsBookFill } from \"react-icons/bs\";\r\nimport \"./style.css\";\r\n\r\n\r\nconst primaryClasses = [\r\n  { value: \"\", label: \"All Classes\" },\r\n  { value: \"1\", label: \"Class 1\" },\r\n  { value: \"2\", label: \"Class 2\" },\r\n  { value: \"3\", label: \"Class 3\" },\r\n  { value: \"4\", label: \"Class 4\" },\r\n  { value: \"5\", label: \"Class 5\" },\r\n  { value: \"6\", label: \"Class 6\" },\r\n  { value: \"7\", label: \"Class 7\" },\r\n];\r\n\r\nconst secondaryClasses = [\r\n  { value: \"\", label: \"All Classes\" },\r\n  { value: \"Form-1\", label: \"Form 1\" },\r\n  { value: \"Form-2\", label: \"Form 2\" },\r\n  { value: \"Form-3\", label: \"Form 3\" },\r\n  { value: \"Form-4\", label: \"Form 4\" },\r\n];\r\n\r\nconst advanceClasses = [\r\n  { value: \"\", label: \"All Classes\" },\r\n  { value: \"Form-5\", label: \"Form 5\" },\r\n  { value: \"Form-6\", label: \"Form 6\" },\r\n];\r\n\r\nfunction Quiz() {\r\n  const [exams, setExams] = useState([]);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [reportsData, setReportsData] = useState([]);\r\n  const [selectedClass, setSelectedClass] = useState(null);\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const { user } = useSelector((state) => state.user);\r\n  const [lgSize, setLgSize] = useState(8);\r\n\r\n  const availableClasses =\r\n    user?.level?.toLowerCase() === \"primary\"\r\n      ? primaryClasses\r\n      : user?.level?.toLowerCase() === \"secondary\"\r\n        ? secondaryClasses\r\n        : advanceClasses;\r\n\r\n  useEffect(() => {\r\n    if (user && user.class) {\r\n      const defaultSelectedClass = availableClasses.find(\r\n        (option) => option.value === user.class\r\n      );\r\n      setSelectedClass(defaultSelectedClass);\r\n    }\r\n  }, [user, availableClasses]);\r\n\r\n  useEffect(() => {\r\n    const updateLgSize = () => {\r\n      setLgSize(window.innerWidth < 1380 ? 9 : 7);\r\n    };\r\n\r\n    // Set initial lg size\r\n    updateLgSize();\r\n\r\n    // Add event listener for window resize\r\n    window.addEventListener(\"resize\", updateLgSize);\r\n\r\n    // Cleanup event listener on component unmount\r\n    return () => {\r\n      window.removeEventListener(\"resize\", updateLgSize);\r\n    };\r\n  }, []);\r\n\r\n  const handleClassChange = (selectedOption) => {\r\n    setSelectedClass(selectedOption);\r\n  };\r\n\r\n  const filteredExams = exams.filter(\r\n    (exam) => {\r\n      // Handle class filtering with format compatibility\r\n      let classMatches = true;\r\n      if (selectedClass && selectedClass.value !== \"\") {\r\n        const selectedValue = selectedClass.value;\r\n        const examClass = exam.class;\r\n\r\n        // Check for exact match first\r\n        if (examClass === selectedValue) {\r\n          classMatches = true;\r\n        }\r\n        // Check if exam class has \"Class-\" prefix and selected value is just the number\r\n        else if (examClass === `Class-${selectedValue}`) {\r\n          classMatches = true;\r\n        }\r\n        // Check if selected value has \"Class-\" prefix and exam class is just the number\r\n        else if (selectedValue === `Class-${examClass}`) {\r\n          classMatches = true;\r\n        }\r\n        // Check for Form classes (secondary)\r\n        else if (examClass === `Form-${selectedValue.replace('Form-', '')}`) {\r\n          classMatches = true;\r\n        }\r\n        else if (selectedValue === `Form-${examClass.replace('Form-', '')}`) {\r\n          classMatches = true;\r\n        }\r\n        else {\r\n          classMatches = false;\r\n        }\r\n      }\r\n\r\n      // Handle search filtering\r\n      const searchMatches = !searchQuery.trim() ||\r\n        exam.name?.toLowerCase().includes(searchQuery.toLowerCase().trim()) ||\r\n        exam.category?.toLowerCase().includes(searchQuery.toLowerCase().trim()) ||\r\n        exam.class?.toLowerCase().includes(searchQuery.toLowerCase().trim());\r\n\r\n      return classMatches && searchMatches;\r\n    }\r\n  );\r\n\r\n  // Debug logging\r\n  if (exams.length > 0) {\r\n    console.log(`📊 Quiz Debug: ${filteredExams.length}/${exams.length} exams shown | Class: ${selectedClass?.label || 'None'} | Search: \"${searchQuery}\"`);\r\n  }\r\n\r\n  const getExams = async () => {\r\n    try {\r\n      console.log(\"🔍 Starting to fetch exams...\");\r\n      dispatch(ShowLoading());\r\n      const response = await getAllExams();\r\n      console.log(\"📡 API Response:\", response);\r\n      if (response.success) {\r\n        console.log(\"✅ Exams fetched successfully:\", response.data.length, \"exams\");\r\n        setExams(response.data.reverse());\r\n      } else {\r\n        console.error(\"❌ API Error:\", response.message);\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      console.error(\"❌ Network Error:\", error);\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const filterReportsData = (data) => {\r\n    const reportsMap = {};\r\n\r\n    // Iterate over the response data (reports)\r\n    data.forEach(report => {\r\n      const examId = report.exam._id;\r\n      const verdict = report.result.verdict;\r\n\r\n      // If the examId is not already in the map, add it\r\n      if (!reportsMap[examId]) {\r\n        reportsMap[examId] = report;\r\n      } else {\r\n        // If there is already an entry for this exam, keep the one with \"pass\" verdict, or just keep the first one if no \"pass\"\r\n        if (verdict === \"Pass\" && reportsMap[examId].result.verdict !== \"Pass\") {\r\n          reportsMap[examId] = report; // Replace with the \"pass\" verdict report\r\n        }\r\n      }\r\n    });\r\n\r\n    return Object.values(reportsMap);\r\n  };\r\n\r\n  const getData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      if (response.success) {\r\n\r\n        setReportsData(filterReportsData(response.data));\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData();\r\n    getExams();\r\n  }, []);\r\n\r\n  const verifyRetake = async (exam) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReportsByUser();\r\n      const retakeCount = response.data.filter(\r\n        (item) => item.exam && item.exam._id === exam._id\r\n      ).length;\r\n      console.log(\"Retake count for exam:\", retakeCount);\r\n    } catch (error) {\r\n      message.error(\"Unable to verify retake\");\r\n      dispatch(HideLoading());\r\n      return;\r\n    }\r\n    dispatch(HideLoading());\r\n    navigate(`/user/write-exam/${exam._id}`);\r\n  };\r\n\r\n  const handleSearch = (e) => {\r\n    setSearchQuery(e.target.value);\r\n  };\r\n\r\n  const shouldRenderFilteredExams = filteredExams.length < exams.length;\r\n\r\n  return (\r\n    user && (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-6\">\r\n        <div className=\"container-modern\">\r\n          {/* Modern Header */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: -20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            className=\"mb-8\"\r\n          >\r\n            <div className=\"text-center mb-6\">\r\n              <h1 className=\"heading-2 text-gradient mb-4\">\r\n                <TbBrain className=\"inline w-10 h-10 mr-3\" />\r\n                Challenge Your Brain, Beat the Rest\r\n              </h1>\r\n              <p className=\"text-xl text-gray-600\">\r\n                Test your knowledge and track your progress with our comprehensive quiz system\r\n              </p>\r\n            </div>\r\n\r\n            {/* User Info Card */}\r\n            <Card className=\"p-6 mb-6 bg-gradient-to-r from-primary-50 to-blue-50 border-primary-200\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center space-x-4\">\r\n                  <div className=\"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\">\r\n                    <span className=\"text-primary-600 font-bold text-lg\">\r\n                      {user?.name?.charAt(0)?.toUpperCase()}\r\n                    </span>\r\n                  </div>\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold text-gray-900\">Welcome back, {user?.name}!</h3>\r\n                    <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\r\n                      <span>Current Class:</span>\r\n                      <span className=\"badge-primary\">\r\n                        {user?.level === \"Primary\"\r\n                          ? `Class ${user?.class}`\r\n                          : user?.level === \"Secondary\"\r\n                          ? `Form ${user?.class?.replace('Form-', '')}`\r\n                          : user?.level === \"Advance\"\r\n                          ? `Form ${user?.class?.replace('Form-', '')}`\r\n                          : user?.class || 'Not Set'}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Quick Stats */}\r\n                <div className=\"hidden md:flex items-center space-x-6\">\r\n                  <div className=\"text-center\">\r\n                    <div className=\"text-2xl font-bold text-primary-600\">{exams.length}</div>\r\n                    <div className=\"text-xs text-gray-500\">Available Quizzes</div>\r\n                  </div>\r\n                  <div className=\"text-center\">\r\n                    <div className=\"text-2xl font-bold text-success-600\">{reportsData.filter(r => r.result?.verdict === \"Pass\").length}</div>\r\n                    <div className=\"text-xs text-gray-500\">Passed</div>\r\n                  </div>\r\n                  <div className=\"text-center\">\r\n                    <div className=\"text-2xl font-bold text-warning-600\">{reportsData.length}</div>\r\n                    <div className=\"text-xs text-gray-500\">Total Attempts</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </Card>\r\n          </motion.div>\r\n\r\n\r\n          {/* Modern Filters */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.2 }}\r\n            className=\"mb-8\"\r\n          >\r\n            <Card className=\"p-6\">\r\n              <div className=\"flex flex-col lg:flex-row gap-6 items-end\">\r\n                {/* Search */}\r\n                <div className=\"flex-1\">\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Search Quizzes\r\n                  </label>\r\n                  <Input\r\n                    placeholder=\"Search by quiz title...\"\r\n                    value={searchQuery}\r\n                    onChange={(e) => handleSearch(e)}\r\n                    icon={<TbSearch />}\r\n                    className=\"w-full\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Class Filter */}\r\n                <div className=\"w-full lg:w-80\">\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Filter by Class\r\n                  </label>\r\n                  <Select\r\n                    options={availableClasses}\r\n                    value={selectedClass}\r\n                    onChange={handleClassChange}\r\n                    placeholder=\"Select Class\"\r\n                    className=\"w-full\"\r\n                    isSearchable={false}\r\n                    styles={{\r\n                      control: (base) => ({\r\n                        ...base,\r\n                        minHeight: '48px',\r\n                        borderColor: '#e5e7eb',\r\n                        '&:hover': { borderColor: '#3b82f6' },\r\n                      }),\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Filter Button */}\r\n                <Button\r\n                  variant=\"secondary\"\r\n                  icon={<TbFilter />}\r\n                  className=\"lg:w-auto w-full\"\r\n                >\r\n                  Filter\r\n                </Button>\r\n              </div>\r\n\r\n              {/* Results Count */}\r\n              {shouldRenderFilteredExams && (\r\n                <div className=\"mt-4 pt-4 border-t border-gray-100\">\r\n                  <span className=\"text-sm text-gray-600\">\r\n                    Showing {filteredExams.length} of {exams.length} quizzes\r\n                  </span>\r\n                </div>\r\n              )}\r\n            </Card>\r\n          </motion.div>\r\n\r\n          {/* Modern Quiz Grid */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.4 }}\r\n          >\r\n            {filteredExams.length > 0 ? (\r\n              <QuizGrid\r\n                quizzes={filteredExams.map(exam => {\r\n                  const examReport = reportsData.find(\r\n                    (report) => report.exam && report.exam._id === exam._id\r\n                  );\r\n\r\n                  return {\r\n                    ...exam,\r\n                    subject: exam.category,\r\n                    questions: exam.questions || [],\r\n                    duration: exam.duration,\r\n                    difficulty: exam.difficulty || 'Medium',\r\n                    attempts: reportsData.filter(r => r.exam?._id === exam._id).length,\r\n                    userResult: examReport ? {\r\n                      percentage: examReport.result?.percentage || 0,\r\n                      correctAnswers: examReport.result?.correctAnswers || 0,\r\n                      totalQuestions: examReport.result?.totalQuestions || exam.questions?.length || 0,\r\n                      verdict: examReport.result?.verdict,\r\n                      completedAt: examReport.createdAt\r\n                    } : null\r\n                  };\r\n                })}\r\n                onQuizStart={(quiz) => verifyRetake(quiz)}\r\n                onQuizView={(quiz) => {\r\n                  const examReport = reportsData.find(\r\n                    (report) => report.exam && report.exam._id === quiz._id\r\n                  );\r\n                  if (examReport) {\r\n                    navigate(`/quiz/${quiz._id}/result`, {\r\n                      state: { result: examReport.result, examData: quiz }\r\n                    });\r\n                  }\r\n                }}\r\n                showResults={true}\r\n                userResults={reportsData.reduce((acc, report) => {\r\n                  if (report.exam?._id) {\r\n                    acc[report.exam._id] = {\r\n                      percentage: report.result?.percentage || 0,\r\n                      correctAnswers: report.result?.correctAnswers?.length || 0,\r\n                      totalQuestions: report.result?.totalQuestions || 0,\r\n                      verdict: report.result?.verdict,\r\n                      completedAt: report.createdAt\r\n                    };\r\n                  }\r\n                  return acc;\r\n                }, {})}\r\n              />\r\n            ) : (\r\n              <Card className=\"p-12 text-center\">\r\n                <TbQuestionMark className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\r\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No Quizzes Found</h3>\r\n                <p className=\"text-gray-600 mb-6\">\r\n                  {searchQuery || selectedClass?.value\r\n                    ? \"Try adjusting your search or filter criteria\"\r\n                    : \"No quizzes are available for your current class level\"}\r\n                </p>\r\n                {(searchQuery || selectedClass?.value) && (\r\n                  <Button\r\n                    variant=\"secondary\"\r\n                    onClick={() => {\r\n                      setSearchQuery(\"\");\r\n                      setSelectedClass({ value: \"\", label: \"All Classes\" });\r\n                    }}\r\n                  >\r\n                    Clear Filters\r\n                  </Button>\r\n                )}\r\n              </Card>\r\n            )}\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    )\r\n  );\r\n}\r\n\r\nexport default Quiz;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Ranking\\index.js", ["494", "495", "496", "497", "498", "499", "500", "501", "502", "503", "504"], [], "import React, { useEffect, useState } from \"react\";\r\nimport './index.css'\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Card, Button, Loading } from \"../../../components/modern\";\r\nimport image from '../../../assets/person.png';\r\nimport { IoPersonCircleOutline } from \"react-icons/io5\";\r\nimport {\r\n  TbTrophy,\r\n  TbMedal,\r\n  TbCrown,\r\n  TbUsers,\r\n  TbSchool,\r\n  TbStar,\r\n  TbChartBar,\r\n  TbUser,\r\n  TbAward\r\n} from \"react-icons/tb\";\r\n\r\nconst Ranking = () => {\r\n    const [rankingData, setRankingData] = useState('');\r\n    const [userRanking, setUserRanking] = useState('');\r\n    const [userData, setUserData] = useState('');\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [isMobile, setIsMobile] = useState(false);\r\n    const [activeTab, setActiveTab] = useState(\"overall\"); // \"overall\" or \"class\"\r\n\r\n\r\n    const dispatch = useDispatch();\r\n\r\n    const fetchReports = async () => {\r\n        try {\r\n            const response = await getAllReportsForRanking();\r\n            if (response.success) {\r\n                setRankingData(response.data);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    }\r\n\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await fetchReports();\r\n                    dispatch(HideLoading());\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (window.innerWidth < 700) {\r\n            setIsMobile(true);\r\n        }\r\n        else {\r\n            setIsMobile(false);\r\n        }\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const getUserStats = () => {\r\n        const Ranking = rankingData\r\n            .map((user, index) => ({\r\n                user,\r\n                ranking: index + 1,\r\n            }))\r\n            .filter((item) => item.user.userId.includes(userData._id));\r\n        setUserRanking(Ranking);\r\n    }\r\n\r\n    useEffect(() => {\r\n        if (rankingData) {\r\n            getUserStats();\r\n        }\r\n    }, [rankingData]);\r\n\r\n    // Helper function to format user ID for mobile devices\r\n    const formatMobileUserId = (userId) => {\r\n        const prefix = userId.slice(0, 4);\r\n        const suffix = userId.slice(-4);\r\n        return `${prefix}.....${suffix}`;\r\n    };\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\r\n            {!isAdmin && (\r\n                <div className=\"container-modern py-8\">\r\n                    {/* Modern Header */}\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: -20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        className=\"text-center mb-8\"\r\n                    >\r\n                        <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full mb-4\">\r\n                            <TbTrophy className=\"w-8 h-8 text-white\" />\r\n                        </div>\r\n                        <h1 className=\"heading-2 text-gradient mb-4\">Leaderboard</h1>\r\n                        <p className=\"text-xl text-gray-600\">\r\n                            See how you rank against other students\r\n                        </p>\r\n                    </motion.div>\r\n\r\n                    {/* Modern Tabs */}\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ delay: 0.2 }}\r\n                        className=\"mb-8\"\r\n                    >\r\n                        <Card className=\"p-2\">\r\n                            <div className=\"flex gap-2\">\r\n                                <motion.button\r\n                                    whileHover={{ scale: 1.02 }}\r\n                                    whileTap={{ scale: 0.98 }}\r\n                                    className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${\r\n                                        activeTab === \"overall\"\r\n                                            ? 'bg-primary-600 text-white shadow-md'\r\n                                            : 'text-gray-600 hover:bg-gray-100'\r\n                                    }`}\r\n                                    onClick={() => setActiveTab(\"overall\")}\r\n                                >\r\n                                    <TbUsers className=\"w-5 h-5\" />\r\n                                    <span>Overall Ranking</span>\r\n                                    {activeTab === \"overall\" && (\r\n                                        <motion.div\r\n                                            layoutId=\"activeRankingTab\"\r\n                                            className=\"w-2 h-2 bg-white rounded-full\"\r\n                                        />\r\n                                    )}\r\n                                </motion.button>\r\n                                <motion.button\r\n                                    whileHover={{ scale: 1.02 }}\r\n                                    whileTap={{ scale: 0.98 }}\r\n                                    className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex-1 justify-center ${\r\n                                        activeTab === \"class\"\r\n                                            ? 'bg-primary-600 text-white shadow-md'\r\n                                            : 'text-gray-600 hover:bg-gray-100'\r\n                                    }`}\r\n                                    onClick={() => setActiveTab(\"class\")}\r\n                                >\r\n                                    <TbSchool className=\"w-5 h-5\" />\r\n                                    <span>Class Ranking</span>\r\n                                    {activeTab === \"class\" && (\r\n                                        <motion.div\r\n                                            layoutId=\"activeRankingTab\"\r\n                                            className=\"w-2 h-2 bg-white rounded-full\"\r\n                                        />\r\n                                    )}\r\n                                </motion.button>\r\n                            </div>\r\n                        </Card>\r\n                    </motion.div>\r\n\r\n                    {/* Modern Leaderboard */}\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ delay: 0.4 }}\r\n                    >\r\n                        {rankingData.length > 0 ? (\r\n                            <Card className=\"overflow-hidden\">\r\n                                {/* Leaderboard Header */}\r\n                                <div className=\"bg-gradient-to-r from-primary-600 to-blue-600 text-white p-6\">\r\n                                    <div className=\"flex items-center justify-center space-x-3\">\r\n                                        <TbTrophy className=\"w-8 h-8 text-yellow-300\" />\r\n                                        <h2 className=\"text-2xl font-bold\">\r\n                                            {activeTab === \"overall\" ? \"Overall Leaderboard\" : \"Class Leaderboard\"}\r\n                                        </h2>\r\n                                    </div>\r\n                                    <p className=\"text-center text-blue-100 mt-2\">\r\n                                        {activeTab === \"overall\"\r\n                                            ? \"Top performers across all classes\"\r\n                                            : `Top performers in ${userData?.class || 'your class'}`\r\n                                        }\r\n                                    </p>\r\n                                </div>\r\n\r\n                                {/* Leaderboard Content */}\r\n                                <div className=\"p-6\">\r\n                                    <div className=\"space-y-4\">\r\n                                        {(activeTab === \"overall\"\r\n                                            ? rankingData\r\n                                            : rankingData.filter(user => user.userClass === userData?.class)\r\n                                        ).map((user, index) => {\r\n                                            const isCurrentUser = user.userId.includes(userData?._id);\r\n                                            const getRankIcon = (position) => {\r\n                                                if (position === 0) return { icon: TbCrown, color: \"text-yellow-500\", bg: \"bg-yellow-50\" };\r\n                                                if (position === 1) return { icon: TbMedal, color: \"text-gray-400\", bg: \"bg-gray-50\" };\r\n                                                if (position === 2) return { icon: TbAward, color: \"text-amber-600\", bg: \"bg-amber-50\" };\r\n                                                return { icon: TbUser, color: \"text-gray-500\", bg: \"bg-gray-50\" };\r\n                                            };\r\n\r\n                                            const rankInfo = getRankIcon(index);\r\n\r\n                                            return (\r\n                                                <motion.div\r\n                                                    key={user.userId}\r\n                                                    initial={{ opacity: 0, x: -20 }}\r\n                                                    animate={{ opacity: 1, x: 0 }}\r\n                                                    transition={{ delay: index * 0.1 }}\r\n                                                    className={`flex items-center space-x-4 p-4 rounded-xl transition-all duration-200 ${\r\n                                                        isCurrentUser\r\n                                                            ? 'bg-primary-50 border-2 border-primary-200 shadow-md'\r\n                                                            : 'bg-gray-50 hover:bg-gray-100'\r\n                                                    }`}\r\n                                                >\r\n                                                    {/* Rank */}\r\n                                                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${rankInfo.bg}`}>\r\n                                                        {index < 3 ? (\r\n                                                            <rankInfo.icon className={`w-6 h-6 ${rankInfo.color}`} />\r\n                                                        ) : (\r\n                                                            <span className=\"font-bold text-gray-700\">{index + 1}</span>\r\n                                                        )}\r\n                                                    </div>\r\n\r\n                                                    {/* Profile Picture */}\r\n                                                    <div className=\"w-12 h-12 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center\">\r\n                                                        {user.userPhoto ? (\r\n                                                            <img\r\n                                                                src={user.userPhoto}\r\n                                                                alt=\"profile\"\r\n                                                                className=\"w-full h-full object-cover\"\r\n                                                                onError={(e) => { e.target.src = image }}\r\n                                                            />\r\n                                                        ) : (\r\n                                                            <TbUser className=\"w-6 h-6 text-gray-400\" />\r\n                                                        )}\r\n                                                    </div>\r\n\r\n                                                    {/* User Info */}\r\n                                                    <div className=\"flex-1 min-w-0\">\r\n                                                        <div className=\"flex items-center space-x-2 mb-1\">\r\n                                                            <h3 className={`font-semibold truncate ${\r\n                                                                isCurrentUser ? 'text-primary-900' : 'text-gray-900'\r\n                                                            }`}>\r\n                                                                {user.userName}\r\n                                                            </h3>\r\n                                                            {isCurrentUser && (\r\n                                                                <span className=\"badge-primary text-xs\">You</span>\r\n                                                            )}\r\n                                                            <span className={`badge-modern text-xs ${\r\n                                                                user.subscriptionStatus === \"active\"\r\n                                                                    ? 'bg-success-100 text-success-800'\r\n                                                                    : 'bg-warning-100 text-warning-800'\r\n                                                            }`}>\r\n                                                                {user.subscriptionStatus === \"active\" ? \"Premium\" : \"Free\"}\r\n                                                            </span>\r\n                                                        </div>\r\n                                                        <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <TbSchool className=\"w-4 h-4\" />\r\n                                                                <span className=\"truncate\">{user.userSchool || 'Not Enrolled'}</span>\r\n                                                            </div>\r\n                                                            <div className=\"flex items-center space-x-1\">\r\n                                                                <TbUsers className=\"w-4 h-4\" />\r\n                                                                <span>{user.userClass || 'Not Enrolled'}</span>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    </div>\r\n\r\n                                                    {/* Score */}\r\n                                                    <div className=\"text-right\">\r\n                                                        <div className={`text-2xl font-bold ${\r\n                                                            isCurrentUser ? 'text-primary-600' : 'text-gray-900'\r\n                                                        }`}>\r\n                                                            {user.score}\r\n                                                        </div>\r\n                                                        <div className=\"text-xs text-gray-500\">points</div>\r\n                                                    </div>\r\n                                                </motion.div>\r\n                                            );\r\n                                        })}\r\n                                    </div>\r\n                                </div>\r\n                            </Card>\r\n                        ) : (\r\n                            <Card className=\"p-12 text-center\">\r\n                                <TbChartBar className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\r\n                                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No Rankings Yet</h3>\r\n                                <p className=\"text-gray-600\">\r\n                                    Complete some quizzes to see your ranking on the leaderboard!\r\n                                </p>\r\n                            </Card>\r\n                        )}\r\n                    </motion.div>\r\n\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Ranking;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Test\\index.js", ["505", "506"], [], "import React, { useEffect, useState, Suspense } from \"react\";\r\nimport './index.css'\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\n\r\nimport { message } from \"antd\";\r\nconst Test = () => {\r\n    const [userData, setUserData] = useState('');\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n\r\n\r\n    useEffect(() => {\r\n        const getUserData = async () => {\r\n            try {\r\n                const response = await getUserInfo();\r\n                if (response.success) {\r\n                    if (response.data.isAdmin) {\r\n                        setIsAdmin(true);\r\n                    } else {\r\n                        setIsAdmin(false);\r\n                        setUserData(response.data);\r\n                    }\r\n                } else {\r\n                    message.error(response.message);\r\n                }\r\n            } catch (error) {\r\n                message.error(error.message);\r\n            }\r\n        };\r\n        if (localStorage.getItem(\"token\")) {\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    return (\r\n        // <Suspense fallback={<div>Loading...</div>}>\r\n        <div className=\"\">\r\n            <div>{userData.name}</div>\r\n            <div>{userData.school}</div>\r\n            <div>{userData.class}</div>\r\n        </div>\r\n        // </Suspense>\r\n    );\r\n}\r\n\r\nexport default Test;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\AboutUs\\index.js", ["507"], [], "import React, { useEffect, useState } from \"react\";\r\nimport './index.css'\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Rate } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { addReview, getAllReviews } from \"../../../apicalls/reviews\";\r\nimport image from '../../../assets/person.png';\r\n\r\nconst AboutUs = () => {\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [userData, setUserData] = useState('');\r\n    const [userRating, setUserRating] = useState('');\r\n    const [userText, setUserText] = useState('');\r\n    const [reviews, setReviews] = useState('');\r\n    const [userOldReview, setUserOldReview] = useState(null);\r\n    const dispatch = useDispatch();\r\n\r\n    const getReviews = async () => {\r\n        try {\r\n            const response = await getAllReviews();\r\n            if (response.success) {\r\n                setReviews(response.data.reverse());\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    }\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await getReviews();\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n        dispatch(HideLoading());\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const handleRatingChange = (value) => {\r\n        setUserRating(value);\r\n    };\r\n\r\n    const handleSubmit = async () => {\r\n        if (userRating === '' || userRating === 0 || userText === '') {\r\n            return;\r\n        }\r\n        try {\r\n            const data = {\r\n                rating: userRating,\r\n                text: userText\r\n            }\r\n            const response = await addReview(data);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                getReviews();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n            dispatch(HideLoading());\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (reviews) {\r\n            const userInReview = reviews.find(review => review.user._id === userData._id);\r\n            setUserOldReview(userInReview);\r\n        }\r\n    }, [reviews, userData]);\r\n\r\n    return (\r\n        <div className=\"AboutUs\">\r\n            {!isAdmin &&\r\n                <>\r\n                    <PageTitle title=\"About Us\" />\r\n                    <div className=\"divider\"></div>\r\n                    <p className=\"info-para\">\r\n                        Welcome to our web application! Lorem ipsum dolor sit amet, consectetur adipiscing elit.\r\n                        Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam,\r\n                        quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\r\n                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\r\n                        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\r\n                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\r\n                        Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\r\n                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.\r\n                        Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\r\n                    </p>\r\n                    {!userOldReview ?\r\n                        <>\r\n                            <h1>Feedback</h1>\r\n                            <p>\r\n                                We strive to provide an exceptional user experience and value your feedback.<br />\r\n                                Please take a moment to rate our web app:\r\n                            </p>\r\n                            <div><b>Rate Your Experience:</b></div>\r\n                            <div className=\"rating\">\r\n                                <div>\r\n                                    <Rate defaultValue={0} onChange={handleRatingChange} />\r\n                                    <br />\r\n                                    <textarea\r\n                                        className=\"rating-text\"\r\n                                        placeholder=\"Share your thoughts...\"\r\n                                        rows={4}\r\n                                        value={userText}\r\n                                        onChange={(e) => setUserText(e.target.value)}\r\n                                    />\r\n                                </div>\r\n                                <button onClick={handleSubmit}>Submit</button>\r\n                            </div>\r\n                        </>\r\n                        :\r\n                        <>\r\n                            <h2>Your Feedback</h2>\r\n                            <div className=\"p-rating-div\">\r\n                                <div className=\"profile-row\">\r\n                                    <img className=\"profile\" src={userOldReview.user.profileImage ? userOldReview.user.profileImage : image} alt=\"profile\" onError={(e) => { e.target.src = image }} />\r\n                                    <p>{userOldReview.user.name}</p>\r\n                                </div>\r\n                                <Rate defaultValue={userOldReview.rating} className=\"rate\" disabled={true} />\r\n                                <br />\r\n                                <div className=\"text\">{userOldReview.text}</div>\r\n                            </div>\r\n                        </>\r\n                    }\r\n                    <h2>Previous Reviews</h2>\r\n                    {reviews ?\r\n                        <div className=\"p-ratings\">\r\n                            {reviews.map((review, index) => (\r\n                                <div key={index}>\r\n                                    {userOldReview?.user._id !== review.user?._id && review.user?._id &&\r\n                                        <div className=\"p-rating-div\">\r\n                                            <div className=\"profile-row\">\r\n                                                <img className=\"profile\" src={review.user.profileImage ? review.user.profileImage : image} alt=\"profile\" onError={(e) => { e.target.src = image }} />\r\n                                                <p>{review.user.name}</p>\r\n                                            </div>\r\n                                            <Rate defaultValue={review.rating} className=\"rate\" disabled={true} />\r\n                                            <br />\r\n                                            <div className=\"text\">{review.text}</div>\r\n                                        </div>\r\n                                    }\r\n                                </div>\r\n                            ))\r\n                            }\r\n                        </div>\r\n                        :\r\n                        <div>\r\n                            No reviews yet.    \r\n                        </div>\r\n                    }\r\n                </>\r\n            }\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default AboutUs;", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AdminReports\\index.js", ["508", "509"], [], "import React from \"react\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { message, Table } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReports } from \"../../../apicalls/reports\";\r\nimport { useEffect } from \"react\";\r\nimport moment from \"moment\";\r\n\r\nfunction AdminReports() {\r\n  const [reportsData, setReportsData] = React.useState([]);\r\n  const [pagination, setPagination] = React.useState({\r\n    current: 1,\r\n    pageSize: 10,\r\n    total: 0, // total number of records\r\n  });\r\n  const dispatch = useDispatch();\r\n  const [filters, setFilters] = React.useState({\r\n    examName: \"\",\r\n    userName: \"\",\r\n  });\r\n\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"examName\",\r\n      render: (text, record) => <>{record.exam.name}</>,\r\n    },\r\n    {\r\n      title: \"User Name\",\r\n      dataIndex: \"userName\",\r\n      render: (text, record) => <>{record.user.name}</>,\r\n    },\r\n    {\r\n      title: \"Date\",\r\n      dataIndex: \"date\",\r\n      render: (text, record) => (\r\n        <>{moment(record.createdAt).format(\"DD-MM-YYYY hh:mm:ss\")}</>\r\n      ),\r\n    },\r\n    {\r\n      title: \"Total Marks\",\r\n      dataIndex: \"totalQuestions\",\r\n      render: (text, record) => <>{record.exam.totalMarks}</>,\r\n    },\r\n    {\r\n      title: \"Passing Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.exam.passingMarks}</>,\r\n    },\r\n    {\r\n      title: \"Obtained Marks\",\r\n      dataIndex: \"correctAnswers\",\r\n      render: (text, record) => <>{record.result?.correctAnswers?.length || 0}</>,\r\n    },\r\n    {\r\n      title: \"Verdict\",\r\n      dataIndex: \"verdict\",\r\n      render: (text, record) => <>{record.result?.verdict || \"N/A\"}</>,\r\n    },\r\n  ];\r\n\r\n  const getData = async (tempFilters, page = 1, limit = 10) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllReports({\r\n        ...tempFilters,\r\n        page,\r\n        limit,\r\n      });\r\n      if (response.success) {\r\n        setReportsData(response.data);\r\n        setPagination({\r\n          ...pagination,\r\n          current: page,\r\n          total: response.pagination.totalReports,\r\n        });\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getData(filters, pagination.current, pagination.pageSize);\r\n  }, [filters, pagination.current]);\r\n\r\n  const handleTableChange = (pagination) => {\r\n    getData(filters, pagination.current, pagination.pageSize);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <PageTitle title=\"Reports\" />\r\n      <div className=\"divider\"></div>\r\n      <div className=\"flex gap-2\">\r\n        <input\r\n          type=\"text\"\r\n          placeholder=\"Exam\"\r\n          value={filters.examName}\r\n          onChange={(e) => setFilters({ ...filters, examName: e.target.value })}\r\n        />\r\n        <input\r\n          type=\"text\"\r\n          placeholder=\"User\"\r\n          value={filters.userName}\r\n          onChange={(e) => setFilters({ ...filters, userName: e.target.value })}\r\n        />\r\n        <button\r\n          className=\"primary-outlined-btn\"\r\n          onClick={() => {\r\n            setFilters({\r\n              examName: \"\",\r\n              userName: \"\",\r\n            });\r\n            getData({\r\n              examName: \"\",\r\n              userName: \"\",\r\n            });\r\n          }}\r\n        >\r\n          Clear\r\n        </button>\r\n        <button\r\n          className=\"primary-contained-btn\"\r\n          onClick={() => getData(filters, 1, pagination.pageSize)}\r\n        >\r\n          Search\r\n        </button>\r\n      </div>\r\n      <Table\r\n  columns={columns}\r\n  dataSource={reportsData}\r\n  className=\"mt-2\"\r\n  pagination={{\r\n    current: pagination.current,\r\n    total: pagination.total,\r\n    showSizeChanger: false, // Disables size changer as per your request\r\n    onChange: (page) => {\r\n      setPagination({\r\n        ...pagination,\r\n        current: page,\r\n      });\r\n      getData(filters, page); // Pass the page, no need to pass pageSize\r\n    },\r\n  }}\r\n/>\r\n\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default AdminReports;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Users\\index.js", ["510", "511", "512", "513", "514"], [], "import { message } from \"antd\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  getAllUsers,\r\n  blockUserById,\r\n  deleteUserById,\r\n} from \"../../../apicalls/users\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { Card, Button, Input, Loading } from \"../../../components/modern\";\r\nimport {\r\n  TbUsers,\r\n  TbSearch,\r\n  TbFilter,\r\n  TbUserCheck,\r\n  TbUserX,\r\n  TbTrash,\r\n  TbEye,\r\n  TbSchool,\r\n  TbMail,\r\n  TbUser\r\n} from \"react-icons/tb\";\r\n\r\nfunction Users() {\r\n  const navigate = useNavigate();\r\n  const [users, setUsers] = useState([]);\r\n  const [filteredUsers, setFilteredUsers] = useState([]);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [filterStatus, setFilterStatus] = useState(\"all\");\r\n  const [loading, setLoading] = useState(false);\r\n  const dispatch = useDispatch();\r\n\r\n  const getUsersData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllUsers();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setUsers(response.users);\r\n        console.log(\"users\", response);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n  const blockUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await blockUserById({\r\n        studentId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteUser = async (studentId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteUserById({ studentId });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(\"User deleted successfully\");\r\n        getUsersData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n\r\n  // Filter users based on search and status\r\n  useEffect(() => {\r\n    let filtered = users;\r\n\r\n    // Filter by search query\r\n    if (searchQuery) {\r\n      filtered = filtered.filter(user =>\r\n        user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.school?.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        user.class?.toLowerCase().includes(searchQuery.toLowerCase())\r\n      );\r\n    }\r\n\r\n    // Filter by status\r\n    if (filterStatus !== \"all\") {\r\n      filtered = filtered.filter(user => {\r\n        if (filterStatus === \"blocked\") return user.isBlocked;\r\n        if (filterStatus === \"active\") return !user.isBlocked;\r\n        return true;\r\n      });\r\n    }\r\n\r\n    setFilteredUsers(filtered);\r\n  }, [users, searchQuery, filterStatus]);\r\n\r\n  useEffect(() => {\r\n    getUsersData();\r\n  }, []);\r\n\r\n  const UserCard = ({ user }) => (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      whileHover={{ y: -2 }}\r\n      transition={{ duration: 0.2 }}\r\n    >\r\n      <Card className=\"p-6 hover:shadow-large\">\r\n        <div className=\"flex items-start justify-between\">\r\n          <div className=\"flex items-start space-x-4\">\r\n            <div className={`w-12 h-12 rounded-full flex items-center justify-center ${\r\n              user.isBlocked ? 'bg-error-100' : 'bg-primary-100'\r\n            }`}>\r\n              <TbUser className={`w-6 h-6 ${user.isBlocked ? 'text-error-600' : 'text-primary-600'}`} />\r\n            </div>\r\n            <div className=\"flex-1\">\r\n              <div className=\"flex items-center space-x-2 mb-2\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900\">{user.name}</h3>\r\n                <span className={`badge-modern ${\r\n                  user.isBlocked ? 'bg-error-100 text-error-800' : 'bg-success-100 text-success-800'\r\n                }`}>\r\n                  {user.isBlocked ? 'Blocked' : 'Active'}\r\n                </span>\r\n              </div>\r\n\r\n              <div className=\"space-y-1 text-sm text-gray-600\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <TbMail className=\"w-4 h-4\" />\r\n                  <span>{user.email}</span>\r\n                </div>\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <TbSchool className=\"w-4 h-4\" />\r\n                  <span>{user.school || 'No school specified'}</span>\r\n                </div>\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <TbUsers className=\"w-4 h-4\" />\r\n                  <span>Class: {user.class || 'Not assigned'}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Button\r\n              variant={user.isBlocked ? \"success\" : \"warning\"}\r\n              size=\"sm\"\r\n              onClick={() => blockUser(user.studentId)}\r\n              icon={user.isBlocked ? <TbUserCheck /> : <TbUserX />}\r\n            >\r\n              {user.isBlocked ? \"Unblock\" : \"Block\"}\r\n            </Button>\r\n\r\n            <Button\r\n              variant=\"error\"\r\n              size=\"sm\"\r\n              onClick={() => {\r\n                if (window.confirm(\"Are you sure you want to delete this user?\")) {\r\n                  deleteUser(user.studentId);\r\n                }\r\n              }}\r\n              icon={<TbTrash />}\r\n            >\r\n              Delete\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </Card>\r\n    </motion.div>\r\n  );\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-6\">\r\n      <div className=\"container-modern\">\r\n        {/* Modern Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"mb-8\"\r\n        >\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <div>\r\n              <h1 className=\"heading-2 text-gradient flex items-center\">\r\n                <TbUsers className=\"w-8 h-8 mr-3\" />\r\n                User Management\r\n              </h1>\r\n              <p className=\"text-gray-600 mt-2\">\r\n                Manage student accounts, permissions, and access controls\r\n              </p>\r\n            </div>\r\n\r\n            {/* Stats Cards */}\r\n            <div className=\"hidden lg:flex space-x-4\">\r\n              <Card className=\"p-4 text-center min-w-[120px]\">\r\n                <div className=\"text-2xl font-bold text-primary-600\">{users.length}</div>\r\n                <div className=\"text-sm text-gray-500\">Total Users</div>\r\n              </Card>\r\n              <Card className=\"p-4 text-center min-w-[120px]\">\r\n                <div className=\"text-2xl font-bold text-success-600\">\r\n                  {users.filter(u => !u.isBlocked).length}\r\n                </div>\r\n                <div className=\"text-sm text-gray-500\">Active</div>\r\n              </Card>\r\n              <Card className=\"p-4 text-center min-w-[120px]\">\r\n                <div className=\"text-2xl font-bold text-error-600\">\r\n                  {users.filter(u => u.isBlocked).length}\r\n                </div>\r\n                <div className=\"text-sm text-gray-500\">Blocked</div>\r\n              </Card>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Modern Filters */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"mb-8\"\r\n        >\r\n          <Card className=\"p-6\">\r\n            <div className=\"flex flex-col lg:flex-row gap-4 items-end\">\r\n              <div className=\"flex-1\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Search Users\r\n                </label>\r\n                <Input\r\n                  placeholder=\"Search by name, email, school, or class...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  icon={<TbSearch />}\r\n                />\r\n              </div>\r\n\r\n              <div className=\"w-full lg:w-48\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Filter by Status\r\n                </label>\r\n                <select\r\n                  value={filterStatus}\r\n                  onChange={(e) => setFilterStatus(e.target.value)}\r\n                  className=\"input-modern\"\r\n                >\r\n                  <option value=\"all\">All Users</option>\r\n                  <option value=\"active\">Active Only</option>\r\n                  <option value=\"blocked\">Blocked Only</option>\r\n                </select>\r\n              </div>\r\n\r\n              <Button\r\n                variant=\"secondary\"\r\n                icon={<TbFilter />}\r\n                onClick={() => {\r\n                  setSearchQuery(\"\");\r\n                  setFilterStatus(\"all\");\r\n                }}\r\n              >\r\n                Clear Filters\r\n              </Button>\r\n            </div>\r\n\r\n            {(searchQuery || filterStatus !== \"all\") && (\r\n              <div className=\"mt-4 pt-4 border-t border-gray-100\">\r\n                <span className=\"text-sm text-gray-600\">\r\n                  Showing {filteredUsers.length} of {users.length} users\r\n                </span>\r\n              </div>\r\n            )}\r\n          </Card>\r\n        </motion.div>\r\n\r\n        {/* Users Grid */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.4 }}\r\n        >\r\n          {loading ? (\r\n            <div className=\"flex justify-center py-12\">\r\n              <Loading text=\"Loading users...\" />\r\n            </div>\r\n          ) : filteredUsers.length > 0 ? (\r\n            <div className=\"space-y-4\">\r\n              {filteredUsers.map((user, index) => (\r\n                <motion.div\r\n                  key={user.studentId}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: index * 0.1 }}\r\n                >\r\n                  <UserCard user={user} />\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <Card className=\"p-12 text-center\">\r\n              <TbUsers className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\r\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No Users Found</h3>\r\n              <p className=\"text-gray-600\">\r\n                {searchQuery || filterStatus !== \"all\"\r\n                  ? \"Try adjusting your search or filter criteria\"\r\n                  : \"No users have been registered yet\"}\r\n              </p>\r\n            </Card>\r\n          )}\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Users;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Register\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Login\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Forum\\index.js", ["515", "516", "517", "518", "519"], [], "import React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Pagination } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport {\r\n  addQuestion,\r\n  addReply,\r\n  getAllQuestions,\r\n  deleteQuestion,\r\n  updateQuestion,\r\n  updateReplyStatus,\r\n} from \"../../../apicalls/forum\";\r\nimport image from \"../../../assets/person.png\";\r\nimport { FaPencilAlt } from \"react-icons/fa\";\r\nimport { MdDelete, MdMessage } from \"react-icons/md\";\r\nimport { FaCheck } from \"react-icons/fa\";\r\n\r\nconst Forum = () => {\r\n  const [isAdmin, setIsAdmin] = useState(false);\r\n  const [userData, setUserData] = useState(\"\");\r\n  const [questions, setQuestions] = useState([]);\r\n  const [expandedReplies, setExpandedReplies] = useState({});\r\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n  const [editQuestion, setEditQuestion] = useState(null);\r\n  const [form] = Form.useForm();\r\n  const [form2] = Form.useForm();\r\n  const dispatch = useDispatch();\r\n  const [replyRefs, setReplyRefs] = useState({});\r\n\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [limit] = useState(10);\r\n  const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n  const fetchQuestions = async (page) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllQuestions({ page, limit }); // Pass query params to API call\r\n      if (response.success) {\r\n        console.log(response.data);\r\n        setQuestions(response.data); // No need to reverse as backend will handle order\r\n        // setCurrentPage(page);\r\n        setTotalQuestions(response.totalQuestions);\r\n        setTotalPages(response.totalPages);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchQuestions(currentPage);\r\n  }, [currentPage, limit]);\r\n\r\n  const handlePageChange = (page) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        if (response.data.isAdmin) {\r\n          setIsAdmin(true);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        } else {\r\n          setIsAdmin(false);\r\n          setUserData(response.data);\r\n          await fetchQuestions();\r\n        }\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const toggleReplies = (questionId) => {\r\n    setExpandedReplies((prevExpandedReplies) => ({\r\n      ...prevExpandedReplies,\r\n      [questionId]: !prevExpandedReplies[questionId],\r\n    }));\r\n  };\r\n\r\n  const handleAskQuestion = async (values) => {\r\n    try {\r\n      const response = await addQuestion(values);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setAskQuestionVisible(false);\r\n        form.resetFields();\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleReply = (questionId) => {\r\n    setReplyQuestionId(questionId);\r\n  };\r\n\r\n  const handleReplySubmit = async (values) => {\r\n    try {\r\n      const payload = {\r\n        questionId: replyQuestionId,\r\n        text: values.text,\r\n      };\r\n      const response = await addReply(payload);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setReplyQuestionId(null);\r\n        form.resetFields();\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n      setReplyRefs((prevRefs) => ({\r\n        ...prevRefs,\r\n        [replyQuestionId]: React.createRef(),\r\n      }));\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  useEffect(() => {\r\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n      replyRefs[replyQuestionId].current.scrollIntoView({ behavior: \"smooth\" });\r\n    }\r\n  }, [replyQuestionId, replyRefs]);\r\n\r\n  const handleEdit = (question) => {\r\n    setEditQuestion(question);\r\n  };\r\n\r\n  const handleDelete = async (question) => {\r\n    try {\r\n      const confirmDelete = window.confirm(\r\n        \"Are you sure you want to delete this question?\"\r\n      );\r\n      if (!confirmDelete) {\r\n        return;\r\n      }\r\n      const response = await deleteQuestion(question._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleUpdateQuestion = async (values) => {\r\n    try {\r\n      const response = await updateQuestion(values, editQuestion._id);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        setEditQuestion(null);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const handleCancelUpdate = () => {\r\n    setEditQuestion(\"\");\r\n  };\r\n  const handleCancelAdd = () => {\r\n    setAskQuestionVisible(false);\r\n    form.resetFields();\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (editQuestion) {\r\n      form2.setFieldsValue({\r\n        title: editQuestion.title,\r\n        body: editQuestion.body,\r\n      });\r\n    } else {\r\n      form2.resetFields();\r\n    }\r\n  }, [editQuestion]);\r\n\r\n  const handleUpdateStatus = async (questionId, replyId, status) => {\r\n    try {\r\n      const response = await updateReplyStatus({ replyId, status }, questionId);\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        await fetchQuestions();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"Forum\">\r\n        <PageTitle title=\"Forum\" />\r\n        <div className=\"divider\"></div>\r\n\r\n        <div>\r\n          <p>\r\n            Welcome to the forum! Feel free to ask questions, share your\r\n            thoughts, and engage with the community.\r\n          </p>\r\n          <Button\r\n            onClick={() => setAskQuestionVisible(true)}\r\n            style={{ marginBottom: 20 }}\r\n          >\r\n            Ask a Question\r\n          </Button>\r\n        </div>\r\n\r\n        {askQuestionVisible && (\r\n          <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\">\r\n            <Form.Item\r\n              name=\"title\"\r\n              label=\"Title\"\r\n              rules={[{ required: true, message: \"Please enter the title\" }]}\r\n            >\r\n              <Input style={{ padding: \"18px 12px\" }} />\r\n            </Form.Item>\r\n            <Form.Item\r\n              name=\"body\"\r\n              label=\"Body\"\r\n              rules={[{ required: true, message: \"Please enter the body\" }]}\r\n            >\r\n              <Input.TextArea />\r\n            </Form.Item>\r\n            <Form.Item>\r\n              <Button type=\"primary\" htmlType=\"submit\">\r\n                Ask Question\r\n              </Button>\r\n              <Button onClick={handleCancelAdd} style={{ marginLeft: 10 }}>\r\n                Cancel\r\n              </Button>\r\n            </Form.Item>\r\n          </Form>\r\n        )}\r\n\r\n        {questions.length === 0 && <div>Loading...</div>}\r\n\r\n        {questions.map((question) => (\r\n          <div key={question._id} className=\"forum-question-container\">\r\n            <div className=\"question\">\r\n              <div className=\"profile-row\">\r\n                <div className=\"profile-details\">\r\n                  <Avatar\r\n                    src={\r\n                      question.user.profileImage\r\n                        ? question.user.profileImage\r\n                        : image\r\n                    }\r\n                    alt=\"profile\"\r\n                    size={50}\r\n                  />\r\n                  <p>{question.user.name}</p>\r\n                  <p className=\"date\">\r\n                    {new Date(question.createdAt).toLocaleString(undefined, {\r\n                      minute: \"numeric\",\r\n                      hour: \"numeric\",\r\n                      day: \"numeric\",\r\n                      month: \"numeric\",\r\n                      year: \"numeric\",\r\n                    })}\r\n                  </p>\r\n                </div>\r\n                {(userData._id === question.user._id || userData.isAdmin) && (\r\n                  <div className=\"icons\">\r\n                    <FaPencilAlt onClick={() => handleEdit(question)} />\r\n                    <MdDelete\r\n                      size={22}\r\n                      color=\"red\"\r\n                      onClick={() => handleDelete(question)}\r\n                    />\r\n                  </div>\r\n                )}\r\n              </div>\r\n              <div className=\"title\">{question.title}</div>\r\n              <div className=\"body\">{question.body}</div>\r\n              <Button onClick={() => toggleReplies(question._id)}>\r\n                {expandedReplies[question._id]\r\n                  ? \"Collapse Replies\"\r\n                  : \"Expand Replies\"}\r\n              </Button>\r\n              <Button onClick={() => handleReply(question._id)}>Reply</Button>\r\n              <Button\r\n                className=\"ml-auto w-fit \"\r\n                style={{ float: \"inline-end\" }}\r\n              >\r\n                <div style={{ display: \"flex\" }}>\r\n                  <span style={{ padding: \"6px\", display: \"flex\" }}>\r\n                    <MdMessage />\r\n                  </span>\r\n                  <span>{question.replies.length}</span>\r\n                </div>\r\n              </Button>\r\n            </div>\r\n            {editQuestion && editQuestion._id === question._id && (\r\n              <Form\r\n                form={form2}\r\n                onFinish={handleUpdateQuestion}\r\n                layout=\"vertical\"\r\n                initialValues={{\r\n                  title: editQuestion.title,\r\n                  body: editQuestion.body,\r\n                }}\r\n              >\r\n                <Form.Item\r\n                  name=\"title\"\r\n                  label=\"Title\"\r\n                  rules={[\r\n                    { required: true, message: \"Please enter the title\" },\r\n                  ]}\r\n                >\r\n                  <Input style={{ padding: \"18px 12px\" }} />\r\n                </Form.Item>\r\n                <Form.Item\r\n                  name=\"body\"\r\n                  label=\"Body\"\r\n                  rules={[{ required: true, message: \"Please enter the body\" }]}\r\n                >\r\n                  <Input.TextArea />\r\n                </Form.Item>\r\n                <Form.Item>\r\n                  <Button type=\"primary\" htmlType=\"submit\">\r\n                    Update Question\r\n                  </Button>\r\n                  <Button\r\n                    onClick={handleCancelUpdate}\r\n                    style={{ marginLeft: 10 }}\r\n                  >\r\n                    Cancel\r\n                  </Button>\r\n                </Form.Item>\r\n              </Form>\r\n            )}\r\n            {expandedReplies[question._id] && (\r\n              <div className=\"replies\">\r\n                {question.replies.map((reply) => (\r\n                  <div\r\n                    key={reply._id}\r\n                    className={`reply ${\r\n                      reply.user.isAdmin\r\n                        ? \"admin-reply\"\r\n                        : reply.isVerified\r\n                        ? \"verified-reply\"\r\n                        : \"\"\r\n                    }`}\r\n                  >\r\n                    {reply.isVerified && <FaCheck color=\"green\" size={30} />}\r\n                    <div>\r\n                      <div className=\"profile-details\">\r\n                        <Avatar\r\n                          src={\r\n                            reply.user.profileImage\r\n                              ? reply.user.profileImage\r\n                              : image\r\n                          }\r\n                          alt=\"profile\"\r\n                          size={50}\r\n                        />\r\n                        <p>{reply.user.name}</p>\r\n                        <p className=\"date\">\r\n                          {new Date(question.createdAt).toLocaleString(\r\n                            undefined,\r\n                            {\r\n                              minute: \"numeric\",\r\n                              hour: \"numeric\",\r\n                              day: \"numeric\",\r\n                              month: \"numeric\",\r\n                              year: \"numeric\",\r\n                            }\r\n                          )}\r\n                        </p>\r\n                      </div>\r\n                      <div className=\"text\">{reply.text}</div>\r\n                      {isAdmin && !reply.user.isAdmin && (\r\n                        <button\r\n                          className=\"verification-btn\"\r\n                          onClick={() =>\r\n                            handleUpdateStatus(\r\n                              question._id,\r\n                              reply._id,\r\n                              !reply.isVerified\r\n                            )\r\n                          }\r\n                        >\r\n                          {!reply.isVerified\r\n                            ? \"Approve Reply\"\r\n                            : \"Disapprove Reply\"}\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n            <div ref={replyRefs[question._id]}>\r\n              {replyQuestionId === question._id && (\r\n                <Form\r\n                  form={form}\r\n                  onFinish={handleReplySubmit}\r\n                  layout=\"vertical\"\r\n                >\r\n                  <Form.Item\r\n                    name=\"text\"\r\n                    label=\"Your Reply\"\r\n                    rules={[\r\n                      { required: true, message: \"Please enter your reply\" },\r\n                    ]}\r\n                  >\r\n                    <Input.TextArea rows={4} />\r\n                  </Form.Item>\r\n                  <Form.Item>\r\n                    <Button type=\"primary\" htmlType=\"submit\">\r\n                      Submit Reply\r\n                    </Button>\r\n                    <Button\r\n                      onClick={() => setReplyQuestionId(null)}\r\n                      style={{ marginLeft: 10 }}\r\n                    >\r\n                      Cancel\r\n                    </Button>\r\n                  </Form.Item>\r\n                </Form>\r\n              )}\r\n            </div>\r\n          </div>\r\n        ))}\r\n\r\n        <Pagination\r\n          current={currentPage}\r\n          total={totalQuestions}\r\n          pageSize={limit}\r\n          onChange={handlePageChange}\r\n          style={{ marginTop: \"20px\", textAlign: \"center\" }}\r\n          showSizeChanger={false}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Forum;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Profile\\index.js", ["520", "521"], [], "import React, { useEffect, useState } from \"react\";\r\nimport \"./index.css\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport {\r\n  getUserInfo,\r\n  updateUserInfo,\r\n  updateUserPhoto,\r\n  sendOTP,\r\n} from \"../../../apicalls/users\";\r\nimport { Form, message, Modal } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\n\r\nconst Profile = () => {\r\n  const [userDetails, setUserDetails] = useState(null);\r\n  const [rankingData, setRankingData] = useState(null);\r\n  const [userRanking, setUserRanking] = useState(null);\r\n  const [edit, setEdit] = useState(false);\r\n  const [imagePreview, setImagePreview] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    email: \"\",\r\n    school: \"\",\r\n    level: \"\", // New field for level\r\n    class_: \"\",\r\n    phoneNumber: \"\",\r\n  });\r\n  const [profileImage, setProfileImage] = useState(null);\r\n  const [serverGeneratedOTP, setServerGeneratedOTP] = useState(null);\r\n  const [showLevelChangeModal, setShowLevelChangeModal] = useState(false);\r\n  const [pendingLevelChange, setPendingLevelChange] = useState(null);\r\n  const dispatch = useDispatch();\r\n\r\n  const fetchReports = async () => {\r\n    try {\r\n      const response = await getAllReportsForRanking();\r\n      if (response.success) {\r\n        setRankingData(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const getUserStats = () => {\r\n    const Ranking = rankingData\r\n      .map((user, index) => ({\r\n        user,\r\n        ranking: index + 1,\r\n      }))\r\n      .filter((item) => item.user.userId.includes(userDetails._id));\r\n    setUserRanking(Ranking);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (rankingData) {\r\n      getUserStats();\r\n    }\r\n  }, [rankingData]);\r\n\r\n  const getUserData = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getUserInfo();\r\n      if (response.success) {\r\n        setUserDetails(response.data);\r\n        setFormData({\r\n          ...formData,\r\n          name: response.data.name,\r\n          email: response.data.email,\r\n          school: response.data.school,\r\n          class_: response.data.class,\r\n          level: response.data.level,\r\n          phoneNumber: response.data.phoneNumber,\r\n        });\r\n        fetchReports();\r\n        if (response.data.profileImage) {\r\n          setProfileImage(response.data.profileImage);\r\n        }\r\n        dispatch(HideLoading());\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (localStorage.getItem(\"token\")) {\r\n      getUserData();\r\n    }\r\n  }, []);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n\r\n    if (name === \"phoneNumber\" && value.length > 10) return; // Limit to 10 digits\r\n\r\n    // Check if level is changing\r\n    if (name === \"level\" && value !== userDetails.level && value !== \"\") {\r\n      setPendingLevelChange(value);\r\n      setShowLevelChangeModal(true);\r\n      return; // Don't update formData yet\r\n    }\r\n\r\n    setFormData((prevFormData) => ({\r\n      ...prevFormData,\r\n      [name]: value,\r\n      ...(name === \"level\" ? { class_: \"\" } : {}),\r\n    }));\r\n  };\r\n\r\n  const discardChanges = () => {\r\n    setFormData({\r\n      ...formData,\r\n      name: userDetails.name,\r\n      email: userDetails.email,\r\n      school: userDetails.school,\r\n      class_: userDetails.class,\r\n      level: userDetails.level,\r\n    });\r\n    setEdit(false);\r\n  };\r\n\r\n  const sendOTPRequest = async (email) => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await sendOTP({ email });\r\n      if (response.success) {\r\n        message.success(\"Please verify new email!\");\r\n        setEdit(false);\r\n        setServerGeneratedOTP(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n        discardChanges();\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n      discardChanges();\r\n    }\r\n    dispatch(HideLoading());\r\n  };\r\n\r\n  const handleUpdate = async ({ skipOTP }) => {\r\n    if (\r\n      formData.name === userDetails.name &&\r\n      formData.email === userDetails.email &&\r\n      formData.school === userDetails.school &&\r\n      formData.class_ === userDetails.class &&\r\n      formData.phoneNumber === userDetails.phoneNumber &&\r\n      formData.level === userDetails.level\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    if (!formData.name) {\r\n      message.error(\"Please enter your name.\");\r\n      return;\r\n    }\r\n\r\n    if (!formData.class_) {\r\n      message.error(\"Please select a class before updating your profile.\");\r\n      return;\r\n    }\r\n\r\n    // Check if any other fields have been updated\r\n    if (\r\n      formData.name === userDetails.name &&\r\n      formData.email === userDetails.email &&\r\n      formData.school === userDetails.school &&\r\n      formData.class_ === userDetails.class &&\r\n      formData.phoneNumber === userDetails.phoneNumber &&\r\n      formData.level === userDetails.level\r\n    ) {\r\n      message.info(\"No changes detected to update.\");\r\n      return;\r\n    }\r\n\r\n    if (!skipOTP && formData.email !== userDetails.email) {\r\n      sendOTPRequest(formData.email);\r\n      return;\r\n    }\r\n    dispatch(ShowLoading());\r\n\r\n    try {\r\n      const response = await updateUserInfo({\r\n        ...formData,\r\n        userId: userDetails._id\r\n      });\r\n      if (response.success) {\r\n        if (response.levelChanged) {\r\n          message.success(response.message);\r\n          // Refresh the page to ensure all components reflect the new level\r\n          setTimeout(() => {\r\n            window.location.reload();\r\n          }, 2000);\r\n        } else {\r\n          message.success(\"Info updated successfully!\");\r\n        }\r\n        setEdit(false);\r\n        setServerGeneratedOTP(null);\r\n        getUserData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n    dispatch(HideLoading());\r\n  };\r\n\r\n  const handleLevelChangeConfirm = () => {\r\n    setFormData((prevFormData) => ({\r\n      ...prevFormData,\r\n      level: pendingLevelChange,\r\n      class_: \"\", // Reset class when level changes\r\n    }));\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleLevelChangeCancel = () => {\r\n    setShowLevelChangeModal(false);\r\n    setPendingLevelChange(null);\r\n  };\r\n\r\n  const handleImageChange = (e) => {\r\n    const file = e.target.files[0];\r\n    setProfileImage(file);\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => {\r\n        setImagePreview(reader.result);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  const handleImageUpload = async () => {\r\n    const formData = new FormData();\r\n    formData.append(\"profileImage\", profileImage);\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await updateUserPhoto(formData);\r\n      if (response.success) {\r\n        message.success(\"Photo updated successfully!\");\r\n        getUserData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const verifyUser = async (values) => {\r\n    if (values.otp === serverGeneratedOTP) {\r\n      handleUpdate({ skipOTP: true });\r\n    } else {\r\n      message.error(\"Invalid OTP\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"Profile\">\r\n      <PageTitle title=\"Profile\" />\r\n      <div className=\"divider\"></div>\r\n      {serverGeneratedOTP ? (\r\n        <div className=\"card p-3 bg-white\">\r\n          <div>\r\n            <h1 className=\"text-2xl\">\r\n              - Verification<i className=\"ri-user-add-line\"></i>\r\n            </h1>\r\n            <div className=\"divider\"></div>\r\n            <Form layout=\"vertical\" className=\"mt-2\" onFinish={verifyUser}>\r\n              <Form.Item name=\"otp\" label=\"OTP\" initialValue=\"\">\r\n                <input type=\"number\" />\r\n              </Form.Item>\r\n              <div className=\"flex flex-col gap-2\">\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"primary-contained-btn mt-2 w-100\"\r\n                >\r\n                  Submit\r\n                </button>\r\n              </div>\r\n            </Form>\r\n          </div>\r\n        </div>\r\n      ) : (\r\n        <>\r\n          <div className=\"profile-picture-container\">\r\n            <div\r\n              className=\"profile-picture\"\r\n              onClick={() =>\r\n                document.getElementById(\"profileImageInput\").click()\r\n              }\r\n            >\r\n              {imagePreview && <img src={imagePreview} alt=\"Profile Preview\" />}\r\n              {profileImage ? (\r\n                <img src={profileImage} alt=\"Profile\" />\r\n              ) : (\r\n                <>\r\n                  <div className=\"overlay\">Upload Image</div>\r\n                </>\r\n              )}\r\n            </div>\r\n            <input\r\n              id=\"profileImageInput\"\r\n              type=\"file\"\r\n              accept=\"image/*\"\r\n              onChange={handleImageChange}\r\n              style={{ display: \"none\" }}\r\n            />\r\n            {profileImage instanceof File && (\r\n              <button className=\"btn btn-mt\" onClick={handleImageUpload}>\r\n                Save\r\n              </button>\r\n            )}\r\n          </div>\r\n          {userRanking && !userDetails.isAdmin && (\r\n            <div className=\"flex flex-row\">\r\n              <h1 className=\"ranking-data\">\r\n                Position:{\" \"}\r\n                {userRanking[0]?.ranking\r\n                  ? `#${userRanking[0].ranking}`\r\n                  : \"Not Ranked\"}\r\n              </h1>\r\n              <h1 className=\"ranking-data\">\r\n                Score:{\" \"}\r\n                {userRanking[0]?.user.score ? userRanking[0].user.score : \"0\"}\r\n              </h1>\r\n            </div>\r\n          )}\r\n          <div className=\"input-container\">\r\n            <label htmlFor=\"name\" className=\"label\">\r\n              User Name\r\n            </label>\r\n            <br />\r\n            <input\r\n              type=\"text\"\r\n              id=\"name\"\r\n              name=\"name\"\r\n              className=\"input\"\r\n              value={formData.name}\r\n              onChange={handleChange}\r\n              disabled={!edit}\r\n            />\r\n          </div>\r\n          <div className=\"input-container\">\r\n            <label htmlFor=\"school\" className=\"label\">\r\n              School\r\n            </label>\r\n            <br />\r\n            <input\r\n              type=\"text\"\r\n              id=\"school\"\r\n              name=\"school\"\r\n              className=\"input\"\r\n              value={formData.school ? formData.school : \"\"}\r\n              onChange={handleChange}\r\n              disabled={!edit}\r\n            />\r\n          </div>\r\n          <div className=\"input-container\">\r\n            <label htmlFor=\"level\" className=\"label\">\r\n              Level\r\n            </label>\r\n            <br />\r\n            <select\r\n              id=\"level\"\r\n              name=\"level\"\r\n              className=\"input\"\r\n              value={formData.level}\r\n              onChange={handleChange}\r\n              disabled={!edit}\r\n            >\r\n              <option value=\"\">Select Level</option>\r\n              <option value=\"Primary\">Primary</option>\r\n              <option value=\"Secondary\">Secondary</option>\r\n              <option value=\"Advance\">Advance</option>\r\n            </select>\r\n          </div>\r\n\r\n          <div className=\"input-container\">\r\n            <label htmlFor=\"class\" className=\"label\">\r\n              Class\r\n            </label>\r\n            <br />\r\n            <select\r\n              id=\"class\"\r\n              name=\"class_\"\r\n              className=\"input\"\r\n              value={formData.class_}\r\n              onChange={handleChange}\r\n              disabled={!edit || !formData.level}\r\n            >\r\n              <option value=\"\">Select Class</option>\r\n\r\n              {formData.level === \"Primary\" && (\r\n                <>\r\n                  <option value=\"1\">1</option>\r\n                  <option value=\"2\">2</option>\r\n                  <option value=\"3\">3</option>\r\n                  <option value=\"4\">4</option>\r\n                  <option value=\"5\">5</option>\r\n                  <option value=\"6\">6</option>\r\n                  <option value=\"7\">7</option>\r\n                </>\r\n              )}\r\n\r\n              {formData.level === \"Secondary\" && (\r\n                <>\r\n                  <option value=\"Form-1\">Form-1</option>\r\n                  <option value=\"Form-2\">Form-2</option>\r\n                  <option value=\"Form-3\">Form-3</option>\r\n                  <option value=\"Form-4\">Form-4</option>\r\n                </>\r\n              )}\r\n\r\n              {formData.level === \"Advance\" && (\r\n                <>\r\n                  <option value=\"Form-5\">Form-5</option>\r\n                  <option value=\"Form-6\">Form-6</option>\r\n                </>\r\n              )}\r\n            </select>\r\n          </div>\r\n          <div className=\"input-container\">\r\n            <label htmlFor=\"email\" className=\"label\">\r\n              Email Address\r\n            </label>\r\n            <br />\r\n            <input\r\n              type=\"text\"\r\n              id=\"email\"\r\n              name=\"email\"\r\n              className=\"input\"\r\n              value={formData.email}\r\n              onChange={handleChange}\r\n              disabled={!edit}\r\n            />\r\n          </div>\r\n          <div className=\"input-container\">\r\n            <label htmlFor=\"email\" className=\"label\">\r\n              Phone Number\r\n            </label>\r\n            <br />\r\n            <input\r\n              type=\"number\"\r\n              id=\"phoneNumber\"\r\n              name=\"phoneNumber\"\r\n              className=\"input\"\r\n              value={formData.phoneNumber}\r\n              onChange={handleChange}\r\n              disabled={!edit}\r\n            />\r\n          </div>\r\n          {!edit ? (\r\n            <div className=\"edit-btn-div\">\r\n              <button className=\"btn\" onClick={(e) => setEdit(true)}>\r\n                Edit\r\n              </button>\r\n            </div>\r\n          ) : (\r\n            <div className=\"btns-container\">\r\n              <button className=\"btn\" onClick={discardChanges}>\r\n                Cancel\r\n              </button>\r\n              <button className=\"btn\" onClick={handleUpdate}>\r\n                Update\r\n              </button>\r\n            </div>\r\n          )}\r\n        </>\r\n      )}\r\n\r\n      {/* Level Change Confirmation Modal */}\r\n      <Modal\r\n        title=\"Confirm Level Change\"\r\n        open={showLevelChangeModal}\r\n        onOk={handleLevelChangeConfirm}\r\n        onCancel={handleLevelChangeCancel}\r\n        okText=\"Yes, Change Level\"\r\n        cancelText=\"Cancel\"\r\n        okButtonProps={{ danger: true }}\r\n      >\r\n        <div>\r\n          <p><strong>Warning:</strong> You are about to change your level from <strong>{userDetails?.level}</strong> to <strong>{pendingLevelChange}</strong>.</p>\r\n          <p>This will:</p>\r\n          <ul>\r\n            <li>Change your access to content for the new level only</li>\r\n            <li>Reset your class selection</li>\r\n            <li>You will no longer see content from your previous level</li>\r\n            <li>This change will take effect immediately</li>\r\n          </ul>\r\n          <p>Are you sure you want to continue?</p>\r\n        </div>\r\n      </Modal>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Profile;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\common\\Home\\index.js", ["522", "523", "524", "525", "526"], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Chat\\index.jsx", ["527", "528"], [], "import React, { useState } from \"react\";\r\nimport axios from \"axios\";\r\nimport \"./index.css\"; // Import the custom CSS\r\nimport { chatWithChatGPT, uploadImg } from \"../../../apicalls/chat\";\r\nimport ContentRenderer from \"../../../components/ContentRenderer\";\r\n\r\nfunction ChatGPTIntegration() {\r\n  const [messages, setMessages] = useState([]);\r\n  const [prompt, setPrompt] = useState(\"\");\r\n  const [imageFile, setImageFile] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const handleChat = async () => {\r\n    if (!prompt.trim() && !imageFile) return;\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      let imageUrl = null;\r\n\r\n      // Step 1: Upload the image to the server (if an image is selected)\r\n      if (imageFile) {\r\n        const formData = new FormData();\r\n        formData.append(\"image\", imageFile);\r\n\r\n        const data = await uploadImg(formData);\r\n\r\n        if (data?.success) {\r\n          imageUrl = data.url; // Extract the S3 URL\r\n          console.log(\"Image URL: \", imageUrl);\r\n        } else {\r\n          throw new Error(\"Image upload failed\");\r\n        }\r\n      }\r\n\r\n      // Step 2: Construct the ChatGPT message payload\r\n      const userMessage = imageUrl\r\n        ? {\r\n          role: \"user\",\r\n          content: [\r\n            { type: \"text\", text: prompt },\r\n            { type: \"image_url\", image_url: { url: imageUrl } },\r\n          ],\r\n        }\r\n        : { role: \"user\", content: prompt };\r\n\r\n      const updatedMessages = [...messages, userMessage];\r\n      setMessages(updatedMessages);\r\n      setPrompt(\"\");\r\n\r\n      // Step 3: Send the payload to ChatGPT\r\n      const chatPayload = { messages: updatedMessages };\r\n\r\n      const chatRes = await chatWithChatGPT(chatPayload);\r\n\r\n      const apiResponse = chatRes?.data;\r\n      console.log(\"API Response: \", apiResponse);\r\n\r\n      // Step 4: Append the assistant's response to the conversation\r\n      setMessages((prev) => [\r\n        ...prev,\r\n        { role: \"assistant\", content: apiResponse },\r\n      ]);\r\n\r\n      setImageFile(null);\r\n    } catch (error) {\r\n      console.error(\"Error during chat:\", error);\r\n      alert(\"An error occurred while processing your request. Please try again.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleKeyPress = (event) => {\r\n    if (event.key === \"Enter\") {\r\n      handleChat(); // Trigger the handleChat function on Enter key\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"chat-container\">\r\n      {/* Chat messages */}\r\n      <div className=\"chat-messages\">\r\n        {messages.map((msg, index) => (\r\n          <div\r\n            key={index}\r\n            className={`message ${msg.role === \"user\" ? \"user-message\" : \"assistant-message\"\r\n              }`}\r\n          >\r\n            <>\r\n              {msg.role === \"assistant\" ? (\r\n                <>\r\n                  {msg?.content ? (\r\n                    <ContentRenderer text={msg.content} />\r\n                  ) : (\r\n                    <p>Unable to get a response from AI</p>\r\n                  )}\r\n                </>\r\n              ) : (\r\n                <>\r\n                  {typeof msg.content === \"string\"\r\n                    ? msg.content\r\n                    : msg.content.map((item, idx) =>\r\n                      item.type === \"text\" ? (\r\n                        <p key={idx}>{item.text}</p>\r\n                      ) : (\r\n                        <img\r\n                          key={idx}\r\n                          src={item.image_url.url}\r\n                          alt=\"User content\"\r\n                          style={{ height: \"100px\" }}\r\n                        />\r\n                      )\r\n                    )}\r\n                </>\r\n              )}\r\n            </>\r\n          </div>\r\n        ))}\r\n        {isLoading && <div className=\"loading-indicator\">Loading...</div>}\r\n      </div>\r\n\r\n      {/* Input and upload */}\r\n      <div className=\"chat-input-container\">\r\n        <textarea\r\n          className=\"chat-input\"\r\n          placeholder=\"Type your message here...\"\r\n          value={prompt}\r\n          onChange={(e) => setPrompt(e.target.value)}\r\n        ></textarea>\r\n        <input\r\n          type=\"file\"\r\n          accept=\"image/*\"\r\n          onChange={(e) => setImageFile(e.target.files[0])}\r\n          style={{ width: \"200px\", borderRadius: \"5px\", marginRight: \"10px\" }}\r\n        />\r\n        <button\r\n          disabled={isLoading}\r\n          className=\"send-button\"\r\n          onClick={handleChat}\r\n        >\r\n          Send\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ChatGPTIntegration;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\payment.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\users.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\exams.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\PageTitle.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\announcements.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\plans.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\chat.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reports.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\ContentRenderer.js", ["529", "530", "531"], [], "import React from 'react';\r\nimport { InlineMath, BlockMath } from 'react-katex';\r\nimport 'katex/dist/katex.min.css';\r\n\r\nconst ContentRenderer = ({ text }) => {\r\n    // Handle undefined, null, or empty text\r\n    if (!text || typeof text !== 'string') {\r\n        return <div></div>;\r\n    }\r\n\r\n    const inlineMathRegex = /\\\\\\(.*?\\\\\\)/g;\r\n    const blockMathRegex = /\\\\\\[.*?\\\\\\]/gs;\r\n    // const boldTextRegex = /(?:\\*\\*.*?\\*\\*)/g;\r\n    const boldTextRegex = /\\*\\*.*?\\*\\*/g;\r\n    // console.log('Text: ', text);\r\n    let modifiedText = text.replace(blockMathRegex, match => match.replace(/\\n/g, '~~NEWLINE~~'));\r\n    const lines = modifiedText.split('\\n');\r\n    // console.log('Lines with symbol: ', lines);\r\n    const restoredLines = lines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n    // console.log('Lines: ', restoredLines);\r\n\r\n\r\n\r\n\r\n    const inlineMathSymbol = \"~~INLINEMATH~~\";\r\n    const blockMathSymbol = \"~~BLOCKMATH~~\";\r\n    const boldSymbol = \"~~BOLD~~\";\r\n\r\n    let newModifiedText = text.replace(blockMathRegex, match => {\r\n        return `~~BLOCKMATH~~${match.replace(/\\n/g, '~~NEWLINE~~')}~~BLOCKMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(inlineMathRegex, match => {\r\n        return `~~INLINEMATH~~${match}~~INLINEMATH~~`;\r\n    });\r\n\r\n    newModifiedText = newModifiedText.replace(boldTextRegex, match => {\r\n        // console.log('Bold Part: ', match);\r\n        return `~~BOLD~~${match.replace(/\\*\\*/g, '')}~~BOLD~~`;\r\n    });\r\n\r\n    const newLines = newModifiedText.split('\\n');\r\n\r\n    const newRestoredLines = newLines.map(line => line.replace(/~~NEWLINE~~/g, `\\\\\\\\`));\r\n\r\n    // console.log('New Modified Text: ', newModifiedText);\r\n\r\n    const newRegex = /(~~INLINEMATH~~\\\\?\\(.*?\\\\?\\)~~INLINEMATH~~|~~BLOCKMATH~~\\\\?\\[.*?\\\\?\\]~~BLOCKMATH~~|~~BOLD~~.*?~~BOLD~~)/;\r\n\r\n    // Debug logging removed to prevent React rendering issues\r\n\r\n    return (\r\n        <div>\r\n            {newRestoredLines.map((line, lineIndex) => (\r\n                <div key={lineIndex}>\r\n                    {line.trim() === '' ?\r\n                        <br key={`br-${lineIndex}`} />\r\n                        :\r\n                        line.split(newRegex).map((part, index) => {\r\n                            if (part.startsWith(boldSymbol) && part.endsWith(boldSymbol)) {\r\n                                return (\r\n                                    <React.Fragment key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BOLD~~/g, '').split(newRegex).map((nestedPart, n_index) => {\r\n                                            if (nestedPart.startsWith(inlineMathSymbol) && nestedPart.endsWith(inlineMathSymbol)) {\r\n                                                return (\r\n                                                    <InlineMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                                    </InlineMath>\r\n                                                );\r\n                                            } else if (nestedPart.startsWith(blockMathSymbol) && nestedPart.endsWith(blockMathSymbol)) {\r\n                                                return (\r\n                                                    <BlockMath key={`${lineIndex}-${index}-${n_index}`}>\r\n                                                        {nestedPart.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                                    </BlockMath>\r\n                                                );\r\n                                            } else {\r\n                                                return (\r\n                                                    <span key={`${lineIndex}-${index}-${n_index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                                        <strong>{nestedPart}</strong>\r\n                                                    </span>\r\n                                                );\r\n                                            }\r\n                                        })}\r\n                                    </React.Fragment>\r\n                                );\r\n                            } else if (part.startsWith(inlineMathSymbol) && part.endsWith(inlineMathSymbol)) {\r\n                                return (\r\n                                    <InlineMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~INLINEMATH~~/g, '').replace(/^\\\\\\(|\\\\\\)$/g, '')}\r\n                                    </InlineMath>\r\n                                );\r\n                            } else if (part.startsWith(blockMathSymbol) && part.endsWith(blockMathSymbol)) {\r\n                                return (\r\n                                    <BlockMath key={`${lineIndex}-${index}`}>\r\n                                        {part.replace(/~~BLOCKMATH~~/g, '').replace(/\\\\[\\[\\]]/g, '')}\r\n                                    </BlockMath>\r\n                                );\r\n                            } else {\r\n                                return (\r\n                                    <span key={`${lineIndex}-${index}`} style={{ whiteSpace: 'pre-wrap' }}>\r\n                                        {part}\r\n                                    </span>\r\n                                );\r\n                            }\r\n                        })}\r\n                </div>\r\n            ))}\r\n        </div>\r\n\r\n    )\r\n};\r\n\r\nexport default ContentRenderer;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\data\\Subjects.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\study.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\Exams\\AddEditQuestion.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\WriteExam\\Instructions.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\StudyMaterial\\PDFModal.js", [], ["532"], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\reviews.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\forum.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\WaitingModal.jsx", ["533", "534"], [], "import React, { useEffect, useState } from \"react\";\r\nimport Mo<PERSON> from \"react-modal\";\r\nimport \"./WaitingModal.css\";\r\n\r\nModal.setAppElement(\"#root\"); // Ensure accessibility for screen readers\r\n\r\nconst WaitingModal = ({ isOpen, onClose }) => {\r\n    return (\r\n        <Modal\r\n            isOpen={isOpen}\r\n            onRequestClose={onClose}\r\n            className=\"waiting-modal-content\"\r\n            overlayClassName=\"waiting-modal-overlay\"\r\n        >\r\n            <div className=\"waiting-modal-header\">\r\n                <h2>Please confirm the payment</h2>\r\n            </div>\r\n            <div className=\"waiting-modal-timer\">\r\n                <svg\r\n                    fill=\"#253864\"\r\n                    version=\"1.1\"\r\n                    id=\"Layer_1\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    viewBox=\"0 0 512 512\"\r\n                    width=\"64px\"\r\n                    height=\"64px\"\r\n                    stroke=\"#253864\"\r\n                >\r\n                    <g>\r\n                        <path d=\"M437.019,74.981C388.668,26.629,324.38,0,256,0S123.332,26.629,74.981,74.981C26.629,123.332,0,187.62,0,256 s26.629,132.668,74.981,181.019C123.332,485.371,187.62,512,256,512c64.518,0,126.15-24.077,173.541-67.796l-10.312-11.178 c-44.574,41.12-102.544,63.766-163.229,63.766c-64.317,0-124.786-25.046-170.266-70.527 C40.254,380.786,15.208,320.317,15.208,256S40.254,131.214,85.734,85.735C131.214,40.254,191.683,15.208,256,15.208 s124.786,25.046,170.266,70.527c45.48,45.479,70.526,105.948,70.526,170.265c0,60.594-22.587,118.498-63.599,163.045 l11.188,10.301C487.986,381.983,512,320.421,512,256C512,187.62,485.371,123.332,437.019,74.981z\"></path>\r\n                        <path d=\"M282.819,263.604h63.415v-15.208h-63.415c-1.619-5.701-5.007-10.662-9.536-14.25l35.913-86.701l-14.049-5.82 l-35.908,86.688c-1.064-0.124-2.142-0.194-3.238-0.194c-15.374,0-27.881,12.508-27.881,27.881s12.507,27.881,27.881,27.881 C268.737,283.881,279.499,275.292,282.819,263.604z M243.327,256c0-6.989,5.685-12.673,12.673-12.673 c6.989,0,12.673,5.685,12.673,12.673c0,6.989-5.685,12.673-12.673,12.673C249.011,268.673,243.327,262.989,243.327,256z\"></path>\r\n                        <path d=\"M451.168,256c0-107.616-87.552-195.168-195.168-195.168S60.832,148.384,60.832,256S148.384,451.168,256,451.168 S451.168,363.616,451.168,256z M76.04,256c0-99.231,80.73-179.96,179.96-179.96S435.96,156.769,435.96,256 S355.231,435.96,256,435.96S76.04,355.231,76.04,256z\"></path>\r\n                    </g>\r\n                </svg>\r\n            </div>\r\n\r\n            <p className=\"waiting-modal-footer\">\r\n                Ensure that your payment is confirmed before the timer runs out.\r\n            </p>\r\n        </Modal>\r\n    );\r\n};\r\n\r\nexport default WaitingModal;\r\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Plans\\components\\ConfirmModal.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\AddStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\SubtitleManager.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\subtitles.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\EditStudyMaterialForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\StudyMaterials\\StudyMaterialManager.js", ["535", "536", "537", "538"], [], "import React, { useState, useEffect } from \"react\";\nimport { Table, Button, Space, Select, Input, message, Modal, Tag, Tooltip } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { \n  getAllStudyMaterials, \n  deleteVideo, \n  deleteNote, \n  deletePastPaper, \n  deleteBook \n} from \"../../../apicalls/study\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport {\n  FaVideo,\n  FaFileAlt,\n  FaBook,\n  FaGraduationCap,\n  FaEdit,\n  FaTrash,\n  FaEye,\n  FaFilter,\n  FaSearch\n} from \"react-icons/fa\";\nimport \"./StudyMaterialManager.css\";\n\nconst { Option } = Select;\nconst { Search } = Input;\n\nfunction StudyMaterialManager({ onEdit }) {\n  const dispatch = useDispatch();\n  const [materials, setMaterials] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [filters, setFilters] = useState({\n    materialType: \"\",\n    level: \"\",\n    className: \"\",\n    subject: \"\"\n  });\n  const [searchText, setSearchText] = useState(\"\");\n\n  // Get subjects based on level\n  const getSubjectsForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return primarySubjects;\n      case \"secondary\":\n        return secondarySubjects;\n      case \"advance\":\n        return advanceSubjects;\n      default:\n        return [];\n    }\n  };\n\n  // Get classes based on level\n  const getClassesForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\"];\n      case \"secondary\":\n        return [\"6\", \"7\", \"8\", \"9\", \"10\", \"11\"];\n      case \"advance\":\n        return [\"12\", \"13\"];\n      default:\n        return [];\n    }\n  };\n\n  // Fetch materials\n  const fetchMaterials = async () => {\n    try {\n      setLoading(true);\n      dispatch(ShowLoading());\n      \n      const response = await getAllStudyMaterials(filters);\n      \n      if (response.status === 200 && response.data.success) {\n        setMaterials(response.data.data || []);\n      } else {\n        message.error(\"Failed to fetch study materials\");\n        setMaterials([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching materials:\", error);\n      message.error(\"Failed to fetch study materials\");\n      setMaterials([]);\n    } finally {\n      setLoading(false);\n      dispatch(HideLoading());\n    }\n  };\n\n  useEffect(() => {\n    fetchMaterials();\n  }, [filters]);\n\n  // Handle filter changes\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => {\n      const newFilters = { ...prev, [key]: value };\n      \n      // Reset dependent filters\n      if (key === \"level\") {\n        newFilters.className = \"\";\n        newFilters.subject = \"\";\n      }\n      \n      return newFilters;\n    });\n  };\n\n  // Handle delete\n  const handleDelete = async (material) => {\n    Modal.confirm({\n      title: `Delete ${material.type.replace(\"-\", \" \")}`,\n      content: `Are you sure you want to delete \"${material.title}\"? This action cannot be undone.`,\n      okText: \"Delete\",\n      okType: \"danger\",\n      cancelText: \"Cancel\",\n      onOk: async () => {\n        try {\n          dispatch(ShowLoading());\n          \n          let response;\n          switch (material.type) {\n            case \"videos\":\n              response = await deleteVideo(material._id);\n              break;\n            case \"study-notes\":\n              response = await deleteNote(material._id);\n              break;\n            case \"past-papers\":\n              response = await deletePastPaper(material._id);\n              break;\n            case \"books\":\n              response = await deleteBook(material._id);\n              break;\n            default:\n              throw new Error(\"Invalid material type\");\n          }\n\n          if (response.status === 200 && response.data.success) {\n            message.success(response.data.message);\n            fetchMaterials(); // Refresh the list\n          } else {\n            message.error(response.data?.message || \"Failed to delete material\");\n          }\n        } catch (error) {\n          console.error(\"Error deleting material:\", error);\n          message.error(\"Failed to delete material\");\n        } finally {\n          dispatch(HideLoading());\n        }\n      }\n    });\n  };\n\n  // Get material type icon\n  const getMaterialIcon = (type) => {\n    switch (type) {\n      case \"videos\":\n        return <FaVideo className=\"material-icon video\" />;\n      case \"study-notes\":\n        return <FaFileAlt className=\"material-icon note\" />;\n      case \"past-papers\":\n        return <FaGraduationCap className=\"material-icon paper\" />;\n      case \"books\":\n        return <FaBook className=\"material-icon book\" />;\n      default:\n        return <FaFileAlt className=\"material-icon\" />;\n    }\n  };\n\n  // Get material type label\n  const getMaterialTypeLabel = (type) => {\n    switch (type) {\n      case \"videos\":\n        return \"Video\";\n      case \"study-notes\":\n        return \"Study Note\";\n      case \"past-papers\":\n        return \"Past Paper\";\n      case \"books\":\n        return \"Book\";\n      default:\n        return type;\n    }\n  };\n\n  // Filter materials based on search text\n  const filteredMaterials = materials.filter(material =>\n    material.title.toLowerCase().includes(searchText.toLowerCase()) ||\n    material.subject.toLowerCase().includes(searchText.toLowerCase()) ||\n    material.className.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // Table columns\n  const columns = [\n    {\n      title: \"Material\",\n      key: \"material\",\n      width: \"30%\",\n      render: (_, record) => (\n        <div className=\"material-info\">\n          <div className=\"material-header\">\n            {getMaterialIcon(record.type)}\n            <div className=\"material-details\">\n              <div className=\"material-title\">{record.title}</div>\n              <div className=\"material-meta\">\n                <Tag color=\"blue\">{getMaterialTypeLabel(record.type)}</Tag>\n                <span className=\"meta-text\">{record.subject} • Class {record.className}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: \"Level\",\n      dataIndex: \"level\",\n      key: \"level\",\n      width: \"10%\",\n      render: (level) => (\n        <Tag color={level === \"primary\" ? \"green\" : level === \"secondary\" ? \"orange\" : \"purple\"}>\n          {level.charAt(0).toUpperCase() + level.slice(1)}\n        </Tag>\n      ),\n    },\n    {\n      title: \"Class\",\n      dataIndex: \"className\",\n      key: \"className\",\n      width: \"10%\",\n      render: (className) => <span className=\"class-badge\">Class {className}</span>,\n    },\n    {\n      title: \"Subject\",\n      dataIndex: \"subject\",\n      key: \"subject\",\n      width: \"15%\",\n    },\n    {\n      title: \"Year\",\n      dataIndex: \"year\",\n      key: \"year\",\n      width: \"10%\",\n      render: (year) => year || \"-\",\n    },\n    {\n      title: \"Actions\",\n      key: \"actions\",\n      width: \"25%\",\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"Edit material\">\n            <Button\n              type=\"primary\"\n              icon={<FaEdit />}\n              size=\"small\"\n              onClick={() => onEdit(record)}\n            >\n              Edit\n            </Button>\n          </Tooltip>\n          \n          <Tooltip title=\"Delete material\">\n            <Button\n              danger\n              icon={<FaTrash />}\n              size=\"small\"\n              onClick={() => handleDelete(record)}\n            >\n              Delete\n            </Button>\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"study-material-manager\">\n      <div className=\"manager-header\">\n        <h2>Study Materials Management</h2>\n        <p>Manage all uploaded study materials - edit, delete, and organize content</p>\n      </div>\n\n      {/* Filters */}\n      <div className=\"filters-section\">\n        <div className=\"filters-row\">\n          <div className=\"filter-group\">\n            <label>Material Type:</label>\n            <Select\n              placeholder=\"All Types\"\n              value={filters.materialType || undefined}\n              onChange={(value) => handleFilterChange(\"materialType\", value)}\n              allowClear\n              style={{ width: 150 }}\n            >\n              <Option value=\"videos\">Videos</Option>\n              <Option value=\"study-notes\">Study Notes</Option>\n              <Option value=\"past-papers\">Past Papers</Option>\n              <Option value=\"books\">Books</Option>\n            </Select>\n          </div>\n\n          <div className=\"filter-group\">\n            <label>Level:</label>\n            <Select\n              placeholder=\"All Levels\"\n              value={filters.level || undefined}\n              onChange={(value) => handleFilterChange(\"level\", value)}\n              allowClear\n              style={{ width: 120 }}\n            >\n              <Option value=\"primary\">Primary</Option>\n              <Option value=\"secondary\">Secondary</Option>\n              <Option value=\"advance\">Advance</Option>\n            </Select>\n          </div>\n\n          {filters.level && (\n            <div className=\"filter-group\">\n              <label>Class:</label>\n              <Select\n                placeholder=\"All Classes\"\n                value={filters.className || undefined}\n                onChange={(value) => handleFilterChange(\"className\", value)}\n                allowClear\n                style={{ width: 120 }}\n              >\n                {getClassesForLevel(filters.level).map(cls => (\n                  <Option key={cls} value={cls}>Class {cls}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n\n          {filters.level && (\n            <div className=\"filter-group\">\n              <label>Subject:</label>\n              <Select\n                placeholder=\"All Subjects\"\n                value={filters.subject || undefined}\n                onChange={(value) => handleFilterChange(\"subject\", value)}\n                allowClear\n                style={{ width: 150 }}\n              >\n                {getSubjectsForLevel(filters.level).map(subject => (\n                  <Option key={subject} value={subject}>{subject}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n\n          <div className=\"filter-group\">\n            <label>Search:</label>\n            <Search\n              placeholder=\"Search materials...\"\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              style={{ width: 200 }}\n              allowClear\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Materials Table */}\n      <div className=\"materials-table\">\n        <Table\n          columns={columns}\n          dataSource={filteredMaterials}\n          rowKey=\"_id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `Total ${total} materials`,\n          }}\n          scroll={{ x: 1000 }}\n        />\n      </div>\n    </div>\n  );\n}\n\nexport default StudyMaterialManager;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\index.js", ["539", "540", "541"], [], "import React, { useState, useEffect } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useDispatch } from \"react-redux\";\nimport { Card, Button, Row, Col, Statistic, Table, Tag, Space, message } from \"antd\";\nimport { \n  FaRobot, \n  FaQuestionCircle, \n  FaHistory, \n  FaCog, \n  FaPlus,\n  FaEye,\n  FaCheck,\n  FaTimes\n} from \"react-icons/fa\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getGenerationHistory } from \"../../../apicalls/aiQuestions\";\nimport QuestionGenerationForm from \"./QuestionGenerationForm\";\nimport QuestionPreview from \"./QuestionPreview\";\nimport \"./AIQuestionGeneration.css\";\n\nfunction AIQuestionGeneration() {\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const [activeView, setActiveView] = useState(\"dashboard\");\n  const [generationHistory, setGenerationHistory] = useState([]);\n  const [selectedGeneration, setSelectedGeneration] = useState(null);\n  const [stats, setStats] = useState({\n    totalGenerations: 0,\n    totalQuestions: 0,\n    approvedQuestions: 0,\n    pendingReview: 0,\n  });\n\n  useEffect(() => {\n    fetchGenerationHistory();\n  }, []);\n\n  const fetchGenerationHistory = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getGenerationHistory({ limit: 20 });\n      if (response.success) {\n        setGenerationHistory(response.data.generations);\n        calculateStats(response.data.generations);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to fetch generation history\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  const calculateStats = (generations) => {\n    const stats = generations.reduce((acc, gen) => {\n      acc.totalGenerations += 1;\n      acc.totalQuestions += gen.generatedQuestions.length;\n      acc.approvedQuestions += gen.generatedQuestions.filter(q => q.approved).length;\n      acc.pendingReview += gen.generationStatus === \"completed\" ? 1 : 0;\n      return acc;\n    }, {\n      totalGenerations: 0,\n      totalQuestions: 0,\n      approvedQuestions: 0,\n      pendingReview: 0,\n    });\n    setStats(stats);\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      pending: \"orange\",\n      in_progress: \"blue\",\n      completed: \"green\",\n      failed: \"red\",\n      cancelled: \"gray\",\n    };\n    return colors[status] || \"default\";\n  };\n\n  const historyColumns = [\n    {\n      title: \"Generation ID\",\n      dataIndex: \"_id\",\n      key: \"_id\",\n      render: (id) => id.slice(-8),\n    },\n    {\n      title: \"Exam\",\n      dataIndex: [\"examId\", \"name\"],\n      key: \"examName\",\n    },\n    {\n      title: \"Questions\",\n      dataIndex: \"generatedQuestions\",\n      key: \"questionCount\",\n      render: (questions) => questions.length,\n    },\n    {\n      title: \"Status\",\n      dataIndex: \"generationStatus\",\n      key: \"status\",\n      render: (status) => (\n        <Tag color={getStatusColor(status)}>\n          {status.toUpperCase()}\n        </Tag>\n      ),\n    },\n    {\n      title: \"Created\",\n      dataIndex: \"createdAt\",\n      key: \"createdAt\",\n      render: (date) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: \"Actions\",\n      key: \"actions\",\n      render: (_, record) => (\n        <Space>\n          <Button\n            type=\"link\"\n            icon={<FaEye />}\n            onClick={() => {\n              setSelectedGeneration(record);\n              setActiveView(\"preview\");\n            }}\n          >\n            Preview\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  const renderDashboard = () => (\n    <div className=\"ai-question-dashboard\">\n      <Row gutter={[16, 16]} className=\"mb-4\">\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Total Generations\"\n              value={stats.totalGenerations}\n              prefix={<FaRobot />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Questions Generated\"\n              value={stats.totalQuestions}\n              prefix={<FaQuestionCircle />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Approved Questions\"\n              value={stats.approvedQuestions}\n              prefix={<FaCheck />}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"Pending Review\"\n              value={stats.pendingReview}\n              prefix={<FaTimes />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Row gutter={[16, 16]}>\n        <Col xs={24} md={12}>\n          <Card\n            title=\"Generate New Questions\"\n            className=\"action-card\"\n            actions={[\n              <Button\n                type=\"primary\"\n                icon={<FaPlus />}\n                onClick={() => setActiveView(\"generate\")}\n              >\n                Start Generation\n              </Button>\n            ]}\n          >\n            <p>Create AI-generated questions for your exams using advanced language models.</p>\n            <ul>\n              <li>Multiple choice questions</li>\n              <li>Fill in the blank questions</li>\n              <li>Picture-based questions</li>\n              <li>Tanzania syllabus compliant</li>\n            </ul>\n          </Card>\n        </Col>\n        <Col xs={24} md={12}>\n          <Card\n            title=\"Generation History\"\n            className=\"action-card\"\n            actions={[\n              <Button\n                icon={<FaHistory />}\n                onClick={() => setActiveView(\"history\")}\n              >\n                View History\n              </Button>\n            ]}\n          >\n            <p>Review and manage your previous question generations.</p>\n            <ul>\n              <li>Track generation status</li>\n              <li>Preview generated questions</li>\n              <li>Approve or reject questions</li>\n              <li>Add approved questions to exams</li>\n            </ul>\n          </Card>\n        </Col>\n      </Row>\n\n      <Card title=\"Recent Generations\" className=\"mt-4\">\n        <Table\n          dataSource={generationHistory.slice(0, 5)}\n          columns={historyColumns}\n          pagination={false}\n          rowKey=\"_id\"\n        />\n        {generationHistory.length > 5 && (\n          <div className=\"text-center mt-3\">\n            <Button onClick={() => setActiveView(\"history\")}>\n              View All Generations\n            </Button>\n          </div>\n        )}\n      </Card>\n    </div>\n  );\n\n  const renderHistory = () => (\n    <Card title=\"Generation History\">\n      <Table\n        dataSource={generationHistory}\n        columns={historyColumns}\n        pagination={{\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n        }}\n        rowKey=\"_id\"\n      />\n    </Card>\n  );\n\n  const renderContent = () => {\n    switch (activeView) {\n      case \"generate\":\n        return (\n          <QuestionGenerationForm\n            onBack={() => setActiveView(\"dashboard\")}\n            onSuccess={() => {\n              setActiveView(\"dashboard\");\n              fetchGenerationHistory();\n            }}\n          />\n        );\n      case \"preview\":\n        return (\n          <QuestionPreview\n            generation={selectedGeneration}\n            onBack={() => setActiveView(\"dashboard\")}\n            onSuccess={() => {\n              setActiveView(\"dashboard\");\n              fetchGenerationHistory();\n            }}\n          />\n        );\n      case \"history\":\n        return renderHistory();\n      default:\n        return renderDashboard();\n    }\n  };\n\n  return (\n    <div className=\"ai-question-generation\">\n      <PageTitle title=\"AI Question Generation\" />\n      \n      {activeView === \"dashboard\" && (\n        <div className=\"page-header\">\n          <h2>AI Question Generation Dashboard</h2>\n          <p>Generate high-quality questions using artificial intelligence</p>\n        </div>\n      )}\n\n      {renderContent()}\n    </div>\n  );\n}\n\nexport default AIQuestionGeneration;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\aiQuestions.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionGenerationForm.js", ["542"], [], "import React, { useState, useEffect, useCallback } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport {\n  Card,\n  Form,\n  Select,\n  InputNumber,\n  Button,\n  Row,\n  Col,\n  Checkbox,\n  message,\n  Divider,\n  Alert,\n  Progress\n} from \"antd\";\nimport { FaArrowLeft, FaRobot } from \"react-icons/fa\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllExams } from \"../../../apicalls/exams\";\nimport {\n  generateQuestions,\n  getSubjectsForLevel,\n  getSyllabusTopics\n} from \"../../../apicalls/aiQuestions\";\nimport AutoGenerateExamModal from \"./AutoGenerateExamModal\";\nimport AILoginModal from \"../../../components/AILoginModal\";\nimport { useAIAuth } from \"../../../hooks/useAIAuth\";\n\nconst { Option } = Select;\n\nfunction QuestionGenerationForm({ onBack, onSuccess }) {\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n\n  // Enhanced authentication\n  const {\n    isAuthenticated,\n    hasAIAccess,\n    user,\n    loading: authLoading,\n    requiresUpgrade,\n    needsLogin,\n    handleLoginSuccess,\n    requireAIAuth,\n    sessionExpiringSoon,\n    timeUntilExpiry\n  } = useAIAuth();\n\n  const [exams, setExams] = useState([]);\n  const [availableSubjects, setAvailableSubjects] = useState([]);\n  const [availableTopics, setAvailableTopics] = useState([]);\n  const [selectedLevel, setSelectedLevel] = useState(\"\");\n  const [selectedClass, setSelectedClass] = useState(\"\");\n  const [selectedSubjects, setSelectedSubjects] = useState([]);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generationProgress, setGenerationProgress] = useState(0);\n  const [showAutoGenerateModal, setShowAutoGenerateModal] = useState(false);\n  const [showLoginModal, setShowLoginModal] = useState(false);\n\n  const fetchExams = useCallback(async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllExams();\n      if (response.success && Array.isArray(response.data)) {\n        // Filter out any null or undefined values\n        const validExams = response.data.filter(exam => exam && exam._id);\n        setExams(validExams);\n      } else {\n        message.error(\"Failed to fetch exams\");\n        setExams([]); // Ensure exams is always an array\n      }\n    } catch (error) {\n      message.error(\"Error fetching exams\");\n      setExams([]); // Ensure exams is always an array\n    } finally {\n      dispatch(HideLoading());\n    }\n  }, [dispatch]);\n\n  useEffect(() => {\n    // Only fetch exams if we have authentication, otherwise let the auth hook handle it\n    if (isAuthenticated && hasAIAccess && !authLoading) {\n      fetchExams();\n    }\n  }, [isAuthenticated, hasAIAccess, authLoading, fetchExams]);\n\n  const handleLevelChange = async (level) => {\n    setSelectedLevel(level);\n    setSelectedClass(\"\");\n    setSelectedSubjects([]);\n    setAvailableTopics([]);\n    form.setFieldsValue({\n      class: undefined,\n      subjects: [],\n      syllabusTopics: []\n    });\n\n    try {\n      const response = await getSubjectsForLevel(level);\n      if (response.success) {\n        setAvailableSubjects(response.data);\n      } else {\n        message.error(\"Failed to fetch subjects\");\n      }\n    } catch (error) {\n      message.error(\"Error fetching subjects\");\n    }\n  };\n\n  const handleClassChange = (className) => {\n    setSelectedClass(className);\n    setAvailableTopics([]);\n    form.setFieldsValue({ syllabusTopics: [] });\n\n    // If subjects are already selected, fetch topics\n    if (selectedSubjects.length > 0) {\n      fetchTopicsForSubjects(selectedLevel, className, selectedSubjects);\n    }\n  };\n\n  const handleSubjectsChange = (subjects) => {\n    setSelectedSubjects(subjects);\n    setAvailableTopics([]);\n    form.setFieldsValue({ syllabusTopics: [] });\n\n    // If class is selected, fetch topics\n    if (selectedClass) {\n      fetchTopicsForSubjects(selectedLevel, selectedClass, subjects);\n    }\n\n    // Note: Auto-generate exam functionality moved to modal\n  };\n\n  const fetchTopicsForSubjects = async (level, className, subjects) => {\n    if (!level || !className || subjects.length === 0) return;\n\n    try {\n      const allTopics = [];\n\n      for (const subject of subjects) {\n        const response = await getSyllabusTopics(level, className, subject);\n        if (response.success) {\n          const subjectTopics = response.data.topics.map(topic => ({\n            ...topic,\n            subject: subject,\n            fullName: `${subject}: ${topic.topicName}`,\n          }));\n          allTopics.push(...subjectTopics);\n        }\n      }\n\n      setAvailableTopics(allTopics);\n    } catch (error) {\n      console.error(\"Error fetching topics:\", error);\n      message.error(\"Failed to fetch syllabus topics\");\n    }\n  };\n\n  const handleAutoGenerateExamSuccess = (newExam) => {\n    // Add the new exam to the list and select it\n    if (newExam && newExam._id) {\n      const updatedExams = [...exams, newExam];\n      setExams(updatedExams);\n      form.setFieldsValue({ examId: newExam._id });\n      setShowAutoGenerateModal(false);\n      message.success(`Exam created successfully: ${newExam.name}`);\n    } else {\n      message.error(\"Invalid exam data received\");\n      setShowAutoGenerateModal(false);\n    }\n  };\n\n  const openAutoGenerateModal = () => {\n    setShowAutoGenerateModal(true);\n  };\n\n  const onFinish = async (values) => {\n    console.log(\"🚀 Form submission started\");\n    console.log(\"📝 Form values:\", values);\n\n    try {\n      setIsGenerating(true);\n      setGenerationProgress(10);\n\n      // Validate question distribution\n      const totalDistribution = Object.values(values.questionDistribution || {}).reduce((sum, count) => sum + (count || 0), 0);\n      console.log(\"📊 Total distribution:\", totalDistribution, \"Total questions:\", values.totalQuestions);\n\n      if (totalDistribution !== values.totalQuestions) {\n        console.error(\"❌ Distribution validation failed\");\n        message.error(\"Question distribution must equal total questions\");\n        setIsGenerating(false);\n        return;\n      }\n\n      console.log(\"✅ Distribution validation passed\");\n\n      setGenerationProgress(30);\n\n      // Check authentication and AI access\n      if (!isAuthenticated || !hasAIAccess) {\n        setIsGenerating(false);\n        setShowLoginModal(true);\n        message.warning(\"Please login to access AI question generation features.\");\n        return;\n      }\n\n      // Double-check with server-side validation\n      const authCheck = await requireAIAuth();\n      if (!authCheck.success) {\n        setIsGenerating(false);\n\n        switch (authCheck.reason) {\n          case 'not_authenticated':\n          case 'refresh_failed':\n            setShowLoginModal(true);\n            message.warning(\"Please login to generate AI questions.\");\n            return;\n          case 'no_ai_access':\n            message.error(\"AI features are not available for your account.\");\n            return;\n          case 'requires_upgrade':\n            message.warning(\"AI question generation requires a premium subscription. Please upgrade your account.\");\n            return;\n          default:\n            setShowLoginModal(true);\n            message.warning(\"Authentication check failed. Please login again.\");\n            return;\n        }\n      }\n\n      const payload = {\n        examId: values.examId,\n        questionTypes: values.questionTypes,\n        subjects: values.subjects,\n        level: values.level,\n        class: values.class,\n        difficultyLevels: values.difficultyLevels,\n        syllabusTopics: values.syllabusTopics || [],\n        totalQuestions: values.totalQuestions,\n        questionDistribution: values.questionDistribution,\n        userId: user._id,\n      };\n\n      console.log(\"📤 Sending payload:\", payload);\n\n      setGenerationProgress(50);\n\n      console.log(\"🌐 Making API call to generate questions...\");\n\n      // Show progress message to user\n      message.info(\"AI is generating your questions... This may take a few minutes.\", 5);\n\n      const response = await generateQuestions(payload);\n      console.log(\"📥 API response received:\", response);\n\n      setGenerationProgress(90);\n\n      if (response.success) {\n        setGenerationProgress(100);\n        message.success(\"Questions generated successfully!\");\n        setTimeout(() => {\n          onSuccess();\n        }, 1000);\n      } else {\n        message.error(response.message || \"Failed to generate questions\");\n      }\n    } catch (error) {\n      console.error(\"Question generation error:\", error);\n\n      // More detailed error handling\n      let errorMessage = \"Error generating questions\";\n\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n        // Handle timeout errors specifically\n        errorMessage = \"Question generation is taking longer than expected. This might be due to high server load. Please try again with fewer questions or check your internet connection.\";\n      } else if (error.response) {\n        // Server responded with error status\n        console.error(\"Server error response:\", error.response.data);\n        console.error(\"Server error status:\", error.response.status);\n\n        if (error.response.status === 401) {\n          // Handle authentication errors for AI requests\n          const errorData = error.response.data;\n\n          if (errorData?.requiresLogin) {\n            // Show the AI login modal instead of redirecting\n            setShowLoginModal(true);\n            errorMessage = errorData.message || \"Authentication required for AI features.\";\n          } else {\n            errorMessage = errorData?.message || \"Authentication failed. Please login again.\";\n          }\n        } else if (error.response.status === 403) {\n          // Handle permission/subscription errors\n          const errorData = error.response.data;\n          if (errorData?.upgradeRequired) {\n            errorMessage = \"AI question generation requires a premium subscription. Please upgrade your account.\";\n          } else {\n            errorMessage = errorData?.message || \"Access denied for AI features.\";\n          }\n        } else if (error.response.status === 504 || error.response.status === 502) {\n          errorMessage = \"Server timeout. The AI generation process is taking longer than expected. Please try again with fewer questions.\";\n        } else {\n          errorMessage = error.response.data?.message || `Server error: ${error.response.status}`;\n        }\n      } else if (error.request) {\n        // Request was made but no response received\n        console.error(\"Network error:\", error.request);\n        errorMessage = \"Network error - please check your connection. If the problem persists, try generating fewer questions at once.\";\n      } else {\n        // Something else happened\n        console.error(\"Error:\", error.message);\n        errorMessage = error.message || \"Unknown error occurred\";\n      }\n\n      message.error(errorMessage);\n    } finally {\n      setIsGenerating(false);\n      setGenerationProgress(0);\n    }\n  };\n\n  const questionTypeOptions = [\n    { label: \"Multiple Choice\", value: \"multiple_choice\" },\n    { label: \"Fill in the Blank\", value: \"fill_blank\" },\n    { label: \"Picture-based\", value: \"picture_based\" },\n  ];\n\n  const difficultyOptions = [\n    { label: \"Easy\", value: \"easy\" },\n    { label: \"Medium\", value: \"medium\" },\n    { label: \"Hard\", value: \"hard\" },\n  ];\n\n  const levelOptions = [\n    { label: \"Primary Education (Standards I-VI)\", value: \"primary\" },\n    { label: \"Ordinary Secondary (Forms I-IV)\", value: \"ordinary_secondary\" },\n    { label: \"Advanced Secondary (Forms V-VI)\", value: \"advanced_secondary\" },\n  ];\n\n  const classOptions = {\n    primary: [\"I\", \"II\", \"III\", \"IV\", \"V\", \"VI\"], // Standards I-VI (TIE Primary)\n    ordinary_secondary: [\"I\", \"II\", \"III\", \"IV\"], // Forms I-IV (TIE Ordinary Secondary)\n    advanced_secondary: [\"V\", \"VI\"], // Forms V-VI (TIE Advanced Secondary)\n  };\n\n  return (\n    <div className=\"question-generation-form\">\n      <Card\n        title={\n          <div className=\"form-header\">\n            <Button\n              type=\"text\"\n              icon={<FaArrowLeft />}\n              onClick={onBack}\n              className=\"back-button\"\n            >\n              Back to Dashboard\n            </Button>\n            <div className=\"title-section\">\n              <FaRobot className=\"title-icon\" />\n              <span>Generate AI Questions</span>\n            </div>\n          </div>\n        }\n      >\n        {/* Authentication Status */}\n        {authLoading ? (\n          <Alert\n            message=\"Checking Authentication...\"\n            description=\"Verifying your access to AI features.\"\n            type=\"info\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : !isAuthenticated ? (\n          <Alert\n            message=\"Login Required\"\n            description={\n              <div>\n                <p>Please login to access AI question generation features.</p>\n                <Button\n                  type=\"primary\"\n                  size=\"small\"\n                  onClick={() => setShowLoginModal(true)}\n                  style={{ marginTop: 8 }}\n                >\n                  Login Now\n                </Button>\n              </div>\n            }\n            type=\"warning\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : !hasAIAccess ? (\n          <Alert\n            message={requiresUpgrade ? \"Upgrade Required\" : \"AI Access Restricted\"}\n            description={\n              requiresUpgrade\n                ? \"AI question generation requires a premium subscription. Please upgrade your account.\"\n                : \"AI features are not available for your account. Please contact support.\"\n            }\n            type=\"error\"\n            showIcon\n            className=\"mb-4\"\n          />\n        ) : sessionExpiringSoon ? (\n          <Alert\n            message=\"Session Expiring Soon\"\n            description={`Your session will expire in ${timeUntilExpiry}. Consider refreshing your login.`}\n            type=\"warning\"\n            showIcon\n            className=\"mb-4\"\n            action={\n              <Button\n                size=\"small\"\n                onClick={() => setShowLoginModal(true)}\n              >\n                Refresh Login\n              </Button>\n            }\n          />\n        ) : (\n          <Alert\n            message=\"AI Features Ready\"\n            description={`Welcome ${user?.name}! You have full access to AI question generation.`}\n            type=\"success\"\n            showIcon\n            className=\"mb-4\"\n          />\n        )}\n\n        {isGenerating && (\n          <Alert\n            message=\"Generating Questions\"\n            description={\n              <div>\n                <p>AI is generating your questions. This may take a few moments...</p>\n                <Progress percent={generationProgress} status=\"active\" />\n              </div>\n            }\n            type=\"info\"\n            showIcon\n            className=\"mb-4\"\n          />\n        )}\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={onFinish}\n          disabled={isGenerating || !hasAIAccess || authLoading}\n        >\n          <Row gutter={[16, 16]}>\n            <Col xs={24}>\n              <Alert\n                message=\"Exam Selection\"\n                description=\"You can either select an existing exam or create a new one using the auto-generate feature. Questions can also be generated independently without an exam.\"\n                type=\"info\"\n                showIcon\n                style={{ marginBottom: 16 }}\n              />\n            </Col>\n\n            <Col xs={24} md={16}>\n              <Form.Item\n                name=\"examId\"\n                label=\"Target Exam (Optional)\"\n                extra=\"Leave empty to generate standalone questions, or select an existing exam\"\n              >\n                <Select\n                  placeholder=\"Optional: Choose an existing exam\"\n                  allowClear\n                >\n                  {exams && exams.length > 0 && exams.map((exam) => (\n                    exam && exam._id ? (\n                      <Option key={exam._id} value={exam._id}>\n                        {exam.name} - {exam.category}\n                      </Option>\n                    ) : null\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item label=\"Or Create New Exam\">\n                <Button\n                  type=\"dashed\"\n                  icon={<FaRobot />}\n                  onClick={openAutoGenerateModal}\n                  style={{ width: \"100%\" }}\n                  disabled={isGenerating || !hasAIAccess || authLoading}\n                >\n                  Auto-Generate New Exam\n                </Button>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"level\"\n                label=\"Education Level\"\n                rules={[{ required: true, message: \"Please select a level\" }]}\n              >\n                <Select \n                  placeholder=\"Choose education level\"\n                  onChange={handleLevelChange}\n                >\n                  {levelOptions.map((option) => (\n                    <Option key={option.value} value={option.value}>\n                      {option.label}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"class\"\n                label=\"Class\"\n                rules={[{ required: true, message: \"Please select a class\" }]}\n              >\n                <Select\n                  placeholder=\"Choose class\"\n                  disabled={!selectedLevel}\n                  onChange={handleClassChange}\n                >\n                  {selectedLevel && classOptions[selectedLevel]?.map((cls) => (\n                    <Option key={cls} value={cls}>\n                      Class {cls}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                name=\"subjects\"\n                label=\"Subjects\"\n                rules={[{ required: true, message: \"Please select at least one subject\" }]}\n              >\n                <Select\n                  mode=\"multiple\"\n                  placeholder=\"Choose subjects\"\n                  disabled={!selectedLevel}\n                  onChange={handleSubjectsChange}\n                >\n                  {availableSubjects.map((subject) => (\n                    <Option key={subject} value={subject}>\n                      {subject}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n\n            <Col xs={24}>\n              <Form.Item\n                name=\"questionTypes\"\n                label=\"Question Types\"\n                rules={[{ required: true, message: \"Please select at least one question type\" }]}\n              >\n                <Checkbox.Group options={questionTypeOptions} />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24}>\n              <Form.Item\n                name=\"difficultyLevels\"\n                label=\"Difficulty Levels\"\n                rules={[{ required: true, message: \"Please select at least one difficulty level\" }]}\n              >\n                <Checkbox.Group options={difficultyOptions} />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name=\"totalQuestions\"\n                label=\"Total Questions\"\n                rules={[\n                  { required: true, message: \"Please enter total questions\" },\n                  { type: \"number\", min: 1, max: 50, message: \"Must be between 1 and 50\" }\n                ]}\n              >\n                <InputNumber\n                  min={1}\n                  max={50}\n                  placeholder=\"Enter total questions\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Divider>Question Distribution</Divider>\n\n          {selectedLevel && selectedClass && selectedSubjects.length > 0 && (\n            <Alert\n              message=\"Tanzania Syllabus Information\"\n              description={\n                <div>\n                  <p><strong>Level:</strong> {selectedLevel.charAt(0).toUpperCase() + selectedLevel.slice(1)}</p>\n                  <p><strong>Class:</strong> {selectedClass}</p>\n                  <p><strong>Subjects:</strong> {selectedSubjects.join(\", \")}</p>\n                  <p><strong>Available Topics:</strong> {availableTopics.length} topics from Tanzania National Curriculum</p>\n                  <p><strong>Auto-generate:</strong> Use the button above to create a new exam with proper structure</p>\n                </div>\n              }\n              type=\"info\"\n              showIcon\n              style={{ marginBottom: 16 }}\n            />\n          )}\n\n          <Row gutter={[16, 16]}>\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"multiple_choice\"]}\n                label=\"Multiple Choice\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"fill_blank\"]}\n                label=\"Fill in the Blank\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={8}>\n              <Form.Item\n                name={[\"questionDistribution\", \"picture_based\"]}\n                label=\"Picture-based\"\n              >\n                <InputNumber\n                  min={0}\n                  placeholder=\"0\"\n                  style={{ width: \"100%\" }}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"syllabusTopics\"\n            label={`Tanzania Syllabus Topics (${availableTopics.length} available)`}\n            extra={availableTopics.length === 0 ? \"Select level, class, and subjects to see available topics\" : \"Select specific topics from Tanzania National Curriculum\"}\n          >\n            <Select\n              mode=\"multiple\"\n              placeholder={availableTopics.length === 0 ? \"No topics available - select level, class, and subjects first\" : \"Choose specific topics from Tanzania syllabus\"}\n              style={{ width: \"100%\" }}\n              disabled={availableTopics.length === 0}\n              optionFilterProp=\"children\"\n              showSearch\n              filterOption={(input, option) =>\n                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0\n              }\n            >\n              {availableTopics.map((topic, index) => (\n                <Option key={`${topic.subject}-${topic.topicName}-${index}`} value={topic.topicName}>\n                  <div>\n                    <strong>{topic.topicName}</strong>\n                    <div style={{ fontSize: \"12px\", color: \"#666\" }}>\n                      {topic.subject} • Difficulty: {topic.difficulty}\n                    </div>\n                    {topic.subtopics && topic.subtopics.length > 0 && (\n                      <div style={{ fontSize: \"11px\", color: \"#999\" }}>\n                        Subtopics: {topic.subtopics.slice(0, 3).join(\", \")}\n                        {topic.subtopics.length > 3 && ` +${topic.subtopics.length - 3} more`}\n                      </div>\n                    )}\n                  </div>\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <div className=\"form-actions\">\n            <Button onClick={onBack} disabled={isGenerating}>\n              Cancel\n            </Button>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={isGenerating}\n              disabled={!hasAIAccess || authLoading}\n              icon={<FaRobot />}\n            >\n              {isGenerating ? \"Generating...\" : !hasAIAccess ? \"Login Required\" : \"Generate Questions\"}\n            </Button>\n          </div>\n        </Form>\n      </Card>\n\n      <AutoGenerateExamModal\n        visible={showAutoGenerateModal}\n        onCancel={() => setShowAutoGenerateModal(false)}\n        onSuccess={handleAutoGenerateExamSuccess}\n        prefilledData={{\n          level: selectedLevel,\n          class: selectedClass,\n          subjects: selectedSubjects,\n        }}\n      />\n\n      <AILoginModal\n        visible={showLoginModal}\n        onCancel={() => setShowLoginModal(false)}\n        onSuccess={(userData) => {\n          handleLoginSuccess(userData);\n          setShowLoginModal(false);\n        }}\n        title=\"AI Features Login Required\"\n        description=\"Please login to access AI question generation features. Your session may have expired or you need enhanced permissions.\"\n      />\n    </div>\n  );\n}\n\nexport default QuestionGenerationForm;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\QuestionPreview.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\admin\\AIQuestionGeneration\\AutoGenerateExamModal.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\utils\\authUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\AILoginModal.js", ["543", "544"], [], "import React, { useState, useEffect } from 'react';\nimport { Modal, Form, Input, Button, Checkbox, Alert, Typography, Space, Divider } from 'antd';\nimport { UserOutlined, LockOutlined, RobotOutlined, ClockCircleOutlined } from '@ant-design/icons';\nimport { quickLogin, autoRefreshToken } from '../apicalls/auth';\nimport { getTokenExpiryInfo } from '../utils/authUtils';\n\nconst { Title, Text } = Typography;\n\nconst AILoginModal = ({ \n  visible, \n  onCancel, \n  onSuccess, \n  title = \"Login Required for AI Features\",\n  description = \"Please login to access AI question generation features.\"\n}) => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [tokenInfo, setTokenInfo] = useState(null);\n  const [autoRefreshing, setAutoRefreshing] = useState(false);\n\n  useEffect(() => {\n    if (visible) {\n      // Check current token status when modal opens\n      const info = getTokenExpiryInfo();\n      setTokenInfo(info);\n      \n      // Try auto-refresh if token is expiring soon\n      if (info.needsRefresh && !info.expired) {\n        handleAutoRefresh();\n      }\n    }\n  }, [visible]);\n\n  const handleAutoRefresh = async () => {\n    try {\n      setAutoRefreshing(true);\n      const success = await autoRefreshToken();\n      if (success) {\n        const newInfo = getTokenExpiryInfo();\n        setTokenInfo(newInfo);\n        \n        if (!newInfo.expired) {\n          onSuccess?.();\n          return;\n        }\n      }\n    } catch (error) {\n      console.error('Auto-refresh failed:', error);\n    } finally {\n      setAutoRefreshing(false);\n    }\n  };\n\n  const handleLogin = async (values) => {\n    try {\n      setLoading(true);\n      \n      const response = await quickLogin({\n        email: values.email,\n        password: values.password,\n        rememberMe: values.rememberMe || false\n      });\n\n      if (response.success) {\n        // Check AI access\n        const { aiAccess } = response.data;\n        \n        if (!aiAccess.enabled) {\n          Modal.warning({\n            title: 'AI Features Not Available',\n            content: aiAccess.requiresUpgrade \n              ? 'AI question generation requires a premium subscription. Please upgrade your account.'\n              : 'AI features are not available for your account. Please contact support.',\n          });\n          return;\n        }\n\n        form.resetFields();\n        onSuccess?.(response.data);\n      }\n    } catch (error) {\n      console.error('Login failed:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderTokenStatus = () => {\n    if (!tokenInfo) return null;\n\n    if (tokenInfo.expired) {\n      return (\n        <Alert\n          type=\"warning\"\n          icon={<ClockCircleOutlined />}\n          message=\"Session Expired\"\n          description=\"Your session has expired. Please login again to continue using AI features.\"\n          style={{ marginBottom: 16 }}\n        />\n      );\n    }\n\n    if (tokenInfo.needsRefresh) {\n      return (\n        <Alert\n          type=\"info\"\n          icon={<ClockCircleOutlined />}\n          message=\"Session Expiring Soon\"\n          description={`Your session will expire in ${tokenInfo.formattedTimeLeft}. Login to extend your session.`}\n          style={{ marginBottom: 16 }}\n        />\n      );\n    }\n\n    return null;\n  };\n\n  return (\n    <Modal\n      title={\n        <Space>\n          <RobotOutlined style={{ color: '#1890ff' }} />\n          <span>{title}</span>\n        </Space>\n      }\n      open={visible}\n      onCancel={onCancel}\n      footer={null}\n      width={450}\n      destroyOnClose\n      maskClosable={false}\n    >\n      <div style={{ padding: '20px 0' }}>\n        <Text type=\"secondary\" style={{ display: 'block', marginBottom: 24, textAlign: 'center' }}>\n          {description}\n        </Text>\n\n        {renderTokenStatus()}\n\n        {autoRefreshing && (\n          <Alert\n            type=\"info\"\n            message=\"Refreshing Session...\"\n            description=\"Attempting to refresh your authentication automatically.\"\n            style={{ marginBottom: 16 }}\n            showIcon\n          />\n        )}\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleLogin}\n          autoComplete=\"off\"\n          size=\"large\"\n        >\n          <Form.Item\n            name=\"email\"\n            label=\"Email\"\n            rules={[\n              { required: true, message: 'Please enter your email' },\n              { type: 'email', message: 'Please enter a valid email' }\n            ]}\n          >\n            <Input\n              prefix={<UserOutlined />}\n              placeholder=\"Enter your email\"\n              autoComplete=\"email\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"password\"\n            label=\"Password\"\n            rules={[{ required: true, message: 'Please enter your password' }]}\n          >\n            <Input.Password\n              prefix={<LockOutlined />}\n              placeholder=\"Enter your password\"\n              autoComplete=\"current-password\"\n            />\n          </Form.Item>\n\n          <Form.Item name=\"rememberMe\" valuePropName=\"checked\">\n            <Checkbox>\n              Keep me logged in for 30 days\n            </Checkbox>\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0 }}>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading || autoRefreshing}\n              block\n              size=\"large\"\n              icon={<RobotOutlined />}\n            >\n              {loading ? 'Logging in...' : autoRefreshing ? 'Refreshing...' : 'Login for AI Features'}\n            </Button>\n          </Form.Item>\n        </Form>\n\n        <Divider />\n\n        <div style={{ textAlign: 'center' }}>\n          <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n            Secure authentication for AI-powered question generation\n          </Text>\n        </div>\n      </div>\n    </Modal>\n  );\n};\n\nexport default AILoginModal;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\hooks\\useAIAuth.js", ["545"], [], "import { useState, useEffect, useCallback } from 'react';\nimport { message } from 'antd';\nimport { validateSession, autoRefreshToken, checkAIAccess } from '../apicalls/auth';\nimport { getTokenExpiryInfo, isSessionValid } from '../utils/authUtils';\n\n/**\n * Enhanced authentication hook specifically for AI features\n * Provides automatic token refresh, session validation, and AI access checking\n */\nexport const useAIAuth = () => {\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [hasAIAccess, setHasAIAccess] = useState(false);\n  const [user, setUser] = useState(null);\n  const [tokenInfo, setTokenInfo] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [requiresUpgrade, setRequiresUpgrade] = useState(false);\n\n  // Check authentication status\n  const checkAuth = useCallback(async () => {\n    try {\n      setLoading(true);\n      \n      // Quick check if session is valid\n      if (!isSessionValid()) {\n        setIsAuthenticated(false);\n        setHasAIAccess(false);\n        setUser(null);\n        setTokenInfo(null);\n        return false;\n      }\n\n      // Get token expiry info\n      const expiry = getTokenExpiryInfo();\n      setTokenInfo(expiry);\n\n      // If token is expired, clear everything\n      if (expiry.expired) {\n        setIsAuthenticated(false);\n        setHasAIAccess(false);\n        setUser(null);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        return false;\n      }\n\n      // Try to auto-refresh if needed\n      if (expiry.needsRefresh) {\n        try {\n          await autoRefreshToken();\n          const newExpiry = getTokenExpiryInfo();\n          setTokenInfo(newExpiry);\n        } catch (error) {\n          console.warn('Auto-refresh failed:', error);\n        }\n      }\n\n      // Validate session and check AI access\n      const accessCheck = await checkAIAccess();\n      \n      if (accessCheck.hasAccess) {\n        setIsAuthenticated(true);\n        setHasAIAccess(true);\n        setUser(accessCheck.user);\n        setRequiresUpgrade(accessCheck.requiresUpgrade || false);\n        return true;\n      } else {\n        setIsAuthenticated(!!accessCheck.user);\n        setHasAIAccess(false);\n        setUser(accessCheck.user || null);\n        setRequiresUpgrade(accessCheck.requiresUpgrade || false);\n        return false;\n      }\n\n    } catch (error) {\n      console.error('Auth check failed:', error);\n      setIsAuthenticated(false);\n      setHasAIAccess(false);\n      setUser(null);\n      setTokenInfo(null);\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  // Refresh authentication\n  const refreshAuth = useCallback(async () => {\n    return await checkAuth();\n  }, [checkAuth]);\n\n  // Logout\n  const logout = useCallback(() => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setIsAuthenticated(false);\n    setHasAIAccess(false);\n    setUser(null);\n    setTokenInfo(null);\n    message.info('Logged out successfully');\n  }, []);\n\n  // Login success handler\n  const handleLoginSuccess = useCallback((userData) => {\n    setIsAuthenticated(true);\n    setUser(userData.user);\n    \n    // Check AI access from login response\n    const aiEnabled = userData.aiAccess?.enabled !== false;\n    setHasAIAccess(aiEnabled);\n    setRequiresUpgrade(userData.aiAccess?.requiresUpgrade || false);\n    \n    // Update token info\n    const expiry = getTokenExpiryInfo();\n    setTokenInfo(expiry);\n    \n    message.success('Successfully logged in for AI features!');\n  }, []);\n\n  // Require authentication for AI operations\n  const requireAIAuth = useCallback(async () => {\n    if (loading) {\n      return { success: false, reason: 'loading' };\n    }\n\n    if (!isAuthenticated) {\n      return { success: false, reason: 'not_authenticated' };\n    }\n\n    if (!hasAIAccess) {\n      if (requiresUpgrade) {\n        return { success: false, reason: 'requires_upgrade' };\n      }\n      return { success: false, reason: 'no_ai_access' };\n    }\n\n    // Check if token is about to expire\n    if (tokenInfo?.needsRefresh) {\n      try {\n        await autoRefreshToken();\n        const newExpiry = getTokenExpiryInfo();\n        setTokenInfo(newExpiry);\n      } catch (error) {\n        return { success: false, reason: 'refresh_failed' };\n      }\n    }\n\n    return { success: true };\n  }, [isAuthenticated, hasAIAccess, requiresUpgrade, tokenInfo, loading]);\n\n  // Auto-refresh timer\n  useEffect(() => {\n    let refreshTimer;\n\n    if (isAuthenticated && tokenInfo && !tokenInfo.expired) {\n      // Set timer to refresh token 5 minutes before expiry\n      const refreshTime = Math.max(0, (tokenInfo.timeLeft - 300) * 1000);\n      \n      refreshTimer = setTimeout(async () => {\n        try {\n          await autoRefreshToken();\n          const newExpiry = getTokenExpiryInfo();\n          setTokenInfo(newExpiry);\n          console.log('🔄 Token auto-refreshed');\n        } catch (error) {\n          console.warn('Auto-refresh timer failed:', error);\n        }\n      }, refreshTime);\n    }\n\n    return () => {\n      if (refreshTimer) {\n        clearTimeout(refreshTimer);\n      }\n    };\n  }, [isAuthenticated, tokenInfo]);\n\n  // Initial auth check\n  useEffect(() => {\n    checkAuth();\n  }, [checkAuth]);\n\n  return {\n    // State\n    isAuthenticated,\n    hasAIAccess,\n    user,\n    tokenInfo,\n    loading,\n    requiresUpgrade,\n    \n    // Actions\n    checkAuth,\n    refreshAuth,\n    logout,\n    handleLoginSuccess,\n    requireAIAuth,\n    \n    // Computed values\n    needsLogin: !isAuthenticated,\n    needsUpgrade: isAuthenticated && !hasAIAccess && requiresUpgrade,\n    sessionExpiringSoon: tokenInfo?.needsRefresh || false,\n    timeUntilExpiry: tokenInfo?.formattedTimeLeft || 'Unknown'\n  };\n};\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\apicalls\\auth.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizResult.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizStart.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Quiz\\QuizPlay.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\QuizRenderer.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\pages\\user\\Hub\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\contexts\\ThemeContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Input.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Button.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Card.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\Loading.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizQuestion.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\QuizTimer.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\LazyImage.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ThemeToggle.js", ["546"], [], "import React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbSun, TbMoon } from 'react-icons/tb';\nimport { useTheme } from '../../contexts/ThemeContext';\n\nconst ThemeToggle = ({ className = '', size = 'md' }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  const sizes = {\n    sm: 'w-8 h-8',\n    md: 'w-10 h-10',\n    lg: 'w-12 h-12',\n  };\n\n  const iconSizes = {\n    sm: 'w-4 h-4',\n    md: 'w-5 h-5',\n    lg: 'w-6 h-6',\n  };\n\n  return (\n    <motion.button\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      onClick={toggleTheme}\n      className={`\n        ${sizes[size]} \n        relative rounded-full p-2 \n        bg-gray-200 dark:bg-gray-700 \n        hover:bg-gray-300 dark:hover:bg-gray-600 \n        transition-all duration-300 \n        focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2\n        ${className}\n      `}\n      aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n    >\n      <div className=\"relative w-full h-full flex items-center justify-center\">\n        <AnimatePresence mode=\"wait\" initial={false}>\n          {isDarkMode ? (\n            <motion.div\n              key=\"sun\"\n              initial={{ y: -20, opacity: 0, rotate: -90 }}\n              animate={{ y: 0, opacity: 1, rotate: 0 }}\n              exit={{ y: 20, opacity: 0, rotate: 90 }}\n              transition={{ duration: 0.3 }}\n              className=\"absolute\"\n            >\n              <TbSun className={`${iconSizes[size]} text-yellow-500`} />\n            </motion.div>\n          ) : (\n            <motion.div\n              key=\"moon\"\n              initial={{ y: -20, opacity: 0, rotate: -90 }}\n              animate={{ y: 0, opacity: 1, rotate: 0 }}\n              exit={{ y: 20, opacity: 0, rotate: 90 }}\n              transition={{ duration: 0.3 }}\n              className=\"absolute\"\n            >\n              <TbMoon className={`${iconSizes[size]} text-blue-600`} />\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.button>\n  );\n};\n\n// Advanced Theme Toggle with Switch Design\nexport const ThemeSwitch = ({ className = '' }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  return (\n    <motion.button\n      whileHover={{ scale: 1.02 }}\n      whileTap={{ scale: 0.98 }}\n      onClick={toggleTheme}\n      className={`\n        relative inline-flex h-6 w-11 items-center rounded-full \n        transition-colors duration-300 focus:outline-none focus:ring-2 \n        focus:ring-primary-500 focus:ring-offset-2\n        ${isDarkMode ? 'bg-primary-600' : 'bg-gray-200'}\n        ${className}\n      `}\n      aria-label={isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'}\n    >\n      <motion.span\n        layout\n        className={`\n          inline-block h-4 w-4 transform rounded-full bg-white shadow-lg \n          transition-transform duration-300 flex items-center justify-center\n          ${isDarkMode ? 'translate-x-6' : 'translate-x-1'}\n        `}\n      >\n        <motion.div\n          initial={false}\n          animate={{ rotate: isDarkMode ? 0 : 180 }}\n          transition={{ duration: 0.3 }}\n        >\n          {isDarkMode ? (\n            <TbMoon className=\"w-2.5 h-2.5 text-primary-600\" />\n          ) : (\n            <TbSun className=\"w-2.5 h-2.5 text-yellow-500\" />\n          )}\n        </motion.div>\n      </motion.span>\n    </motion.button>\n  );\n};\n\n// Theme Toggle with Label\nexport const ThemeToggleWithLabel = ({ className = '', showLabel = true }) => {\n  const { isDarkMode, toggleTheme } = useTheme();\n\n  return (\n    <div className={`flex items-center space-x-3 ${className}`}>\n      {showLabel && (\n        <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n          {isDarkMode ? 'Dark Mode' : 'Light Mode'}\n        </span>\n      )}\n      <ThemeSwitch />\n    </div>\n  );\n};\n\nexport default ThemeToggle;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ErrorBoundary.js", [], [], "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\ResponsiveContainer.js", ["547"], [], "import React from 'react';\nimport { motion } from 'framer-motion';\n\nconst ResponsiveContainer = ({ \n  children, \n  className = '', \n  maxWidth = '7xl',\n  padding = 'responsive',\n  ...props \n}) => {\n  const maxWidths = {\n    'sm': 'max-w-sm',\n    'md': 'max-w-md',\n    'lg': 'max-w-lg',\n    'xl': 'max-w-xl',\n    '2xl': 'max-w-2xl',\n    '3xl': 'max-w-3xl',\n    '4xl': 'max-w-4xl',\n    '5xl': 'max-w-5xl',\n    '6xl': 'max-w-6xl',\n    '7xl': 'max-w-7xl',\n    'full': 'max-w-full',\n  };\n\n  const paddings = {\n    'none': '',\n    'sm': 'px-4',\n    'md': 'px-6',\n    'lg': 'px-8',\n    'responsive': 'px-4 sm:px-6 lg:px-8',\n  };\n\n  return (\n    <div \n      className={`${maxWidths[maxWidth]} mx-auto ${paddings[padding]} ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Responsive Grid Component\nexport const ResponsiveGrid = ({ \n  children, \n  cols = { xs: 1, sm: 2, md: 3, lg: 4 },\n  gap = 6,\n  className = '',\n  ...props \n}) => {\n  const gridCols = {\n    1: 'grid-cols-1',\n    2: 'grid-cols-2',\n    3: 'grid-cols-3',\n    4: 'grid-cols-4',\n    5: 'grid-cols-5',\n    6: 'grid-cols-6',\n  };\n\n  const gaps = {\n    2: 'gap-2',\n    4: 'gap-4',\n    6: 'gap-6',\n    8: 'gap-8',\n  };\n\n  const responsiveClasses = [\n    cols.xs && gridCols[cols.xs],\n    cols.sm && `sm:${gridCols[cols.sm]}`,\n    cols.md && `md:${gridCols[cols.md]}`,\n    cols.lg && `lg:${gridCols[cols.lg]}`,\n    cols.xl && `xl:${gridCols[cols.xl]}`,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div \n      className={`grid ${responsiveClasses} ${gaps[gap]} ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Responsive Text Component\nexport const ResponsiveText = ({ \n  children, \n  size = { xs: 'sm', sm: 'base', md: 'lg' },\n  weight = 'normal',\n  className = '',\n  ...props \n}) => {\n  const textSizes = {\n    'xs': 'text-xs',\n    'sm': 'text-sm',\n    'base': 'text-base',\n    'lg': 'text-lg',\n    'xl': 'text-xl',\n    '2xl': 'text-2xl',\n    '3xl': 'text-3xl',\n    '4xl': 'text-4xl',\n  };\n\n  const fontWeights = {\n    'light': 'font-light',\n    'normal': 'font-normal',\n    'medium': 'font-medium',\n    'semibold': 'font-semibold',\n    'bold': 'font-bold',\n  };\n\n  const responsiveClasses = [\n    size.xs && textSizes[size.xs],\n    size.sm && `sm:${textSizes[size.sm]}`,\n    size.md && `md:${textSizes[size.md]}`,\n    size.lg && `lg:${textSizes[size.lg]}`,\n    size.xl && `xl:${textSizes[size.xl]}`,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <span \n      className={`${responsiveClasses} ${fontWeights[weight]} ${className}`}\n      {...props}\n    >\n      {children}\n    </span>\n  );\n};\n\n// Mobile-First Responsive Component\nexport const MobileFirst = ({ children, className = '' }) => {\n  return (\n    <div className={`block lg:hidden ${className}`}>\n      {children}\n    </div>\n  );\n};\n\n// Desktop-First Responsive Component\nexport const DesktopFirst = ({ children, className = '' }) => {\n  return (\n    <div className={`hidden lg:block ${className}`}>\n      {children}\n    </div>\n  );\n};\n\n// Responsive Stack Component\nexport const ResponsiveStack = ({ \n  children, \n  direction = { xs: 'col', md: 'row' },\n  spacing = 4,\n  align = 'start',\n  justify = 'start',\n  className = '',\n  ...props \n}) => {\n  const directions = {\n    'row': 'flex-row',\n    'col': 'flex-col',\n    'row-reverse': 'flex-row-reverse',\n    'col-reverse': 'flex-col-reverse',\n  };\n\n  const spacings = {\n    2: 'gap-2',\n    4: 'gap-4',\n    6: 'gap-6',\n    8: 'gap-8',\n  };\n\n  const alignments = {\n    'start': 'items-start',\n    'center': 'items-center',\n    'end': 'items-end',\n    'stretch': 'items-stretch',\n  };\n\n  const justifications = {\n    'start': 'justify-start',\n    'center': 'justify-center',\n    'end': 'justify-end',\n    'between': 'justify-between',\n    'around': 'justify-around',\n    'evenly': 'justify-evenly',\n  };\n\n  const responsiveClasses = [\n    direction.xs && directions[direction.xs],\n    direction.sm && `sm:${directions[direction.sm]}`,\n    direction.md && `md:${directions[direction.md]}`,\n    direction.lg && `lg:${directions[direction.lg]}`,\n    direction.xl && `xl:${directions[direction.xl]}`,\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div \n      className={`flex ${responsiveClasses} ${spacings[spacing]} ${alignments[align]} ${justifications[justify]} ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Responsive Show/Hide Component\nexport const ResponsiveShow = ({ \n  children, \n  breakpoint = 'md',\n  direction = 'up',\n  className = '' \n}) => {\n  const breakpoints = {\n    'sm': direction === 'up' ? 'sm:block' : 'sm:hidden',\n    'md': direction === 'up' ? 'md:block' : 'md:hidden',\n    'lg': direction === 'up' ? 'lg:block' : 'lg:hidden',\n    'xl': direction === 'up' ? 'xl:block' : 'xl:hidden',\n  };\n\n  const baseClass = direction === 'up' ? 'hidden' : 'block';\n\n  return (\n    <div className={`${baseClass} ${breakpoints[breakpoint]} ${className}`}>\n      {children}\n    </div>\n  );\n};\n\nexport default ResponsiveContainer;\n", "C:\\Users\\<USER>\\Desktop\\20\\New folder\\client\\src\\components\\modern\\PerformanceMonitor.js", ["548"], [], "import React, { useEffect, useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\n// Performance monitoring hook\nexport const usePerformanceMonitor = () => {\n  const [metrics, setMetrics] = useState({\n    loadTime: 0,\n    renderTime: 0,\n    memoryUsage: 0,\n    fps: 0,\n  });\n\n  useEffect(() => {\n    // Measure page load time\n    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;\n    \n    // Measure memory usage (if available)\n    const memoryUsage = performance.memory ? performance.memory.usedJSHeapSize / 1048576 : 0; // MB\n\n    setMetrics(prev => ({\n      ...prev,\n      loadTime,\n      memoryUsage,\n    }));\n\n    // FPS monitoring\n    let frameCount = 0;\n    let lastTime = performance.now();\n    \n    const measureFPS = () => {\n      frameCount++;\n      const currentTime = performance.now();\n      \n      if (currentTime >= lastTime + 1000) {\n        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));\n        setMetrics(prev => ({ ...prev, fps }));\n        frameCount = 0;\n        lastTime = currentTime;\n      }\n      \n      requestAnimationFrame(measureFPS);\n    };\n    \n    requestAnimationFrame(measureFPS);\n  }, []);\n\n  return metrics;\n};\n\n// Performance indicator component\nconst PerformanceIndicator = ({ show = false }) => {\n  const metrics = usePerformanceMonitor();\n  const [isVisible, setIsVisible] = useState(show);\n\n  useEffect(() => {\n    const handleKeyPress = (e) => {\n      if (e.ctrlKey && e.shiftKey && e.key === 'P') {\n        setIsVisible(!isVisible);\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [isVisible]);\n\n  if (process.env.NODE_ENV !== 'development' && !show) return null;\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: 20 }}\n          className=\"fixed bottom-4 right-4 z-50 bg-black/80 text-white p-4 rounded-lg text-xs font-mono backdrop-blur-sm\"\n        >\n          <div className=\"space-y-1\">\n            <div className=\"font-bold text-green-400 mb-2\">Performance Metrics</div>\n            <div>Load Time: {metrics.loadTime}ms</div>\n            <div>Memory: {metrics.memoryUsage.toFixed(1)}MB</div>\n            <div>FPS: {metrics.fps}</div>\n            <div className=\"text-gray-400 mt-2 text-xs\">\n              Press Ctrl+Shift+P to toggle\n            </div>\n          </div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n};\n\n// Lazy loading wrapper with intersection observer\nexport const LazyWrapper = ({ children, threshold = 0.1, rootMargin = '50px' }) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [ref, setRef] = useState(null);\n\n  useEffect(() => {\n    if (!ref) return;\n\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n          observer.disconnect();\n        }\n      },\n      { threshold, rootMargin }\n    );\n\n    observer.observe(ref);\n    return () => observer.disconnect();\n  }, [ref, threshold, rootMargin]);\n\n  return (\n    <div ref={setRef}>\n      {isVisible ? children : <div className=\"h-32 bg-gray-100 animate-pulse rounded\" />}\n    </div>\n  );\n};\n\n// Optimized image component with WebP support\nexport const OptimizedImage = ({ \n  src, \n  webpSrc, \n  alt, \n  className = '',\n  loading = 'lazy',\n  ...props \n}) => {\n  const [imageError, setImageError] = useState(false);\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  const handleError = () => {\n    setImageError(true);\n  };\n\n  const handleLoad = () => {\n    setIsLoaded(true);\n  };\n\n  return (\n    <div className={`relative overflow-hidden ${className}`}>\n      {!isLoaded && (\n        <div className=\"absolute inset-0 bg-gray-200 animate-pulse\" />\n      )}\n      \n      {!imageError ? (\n        <picture>\n          {webpSrc && <source srcSet={webpSrc} type=\"image/webp\" />}\n          <motion.img\n            src={src}\n            alt={alt}\n            loading={loading}\n            onError={handleError}\n            onLoad={handleLoad}\n            className={`w-full h-full object-cover transition-opacity duration-300 ${\n              isLoaded ? 'opacity-100' : 'opacity-0'\n            }`}\n            {...props}\n          />\n        </picture>\n      ) : (\n        <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\n          <span className=\"text-gray-400 text-sm\">Image not available</span>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Debounced search hook\nexport const useDebouncedSearch = (searchTerm, delay = 300) => {\n  const [debouncedTerm, setDebouncedTerm] = useState(searchTerm);\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedTerm(searchTerm);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [searchTerm, delay]);\n\n  return debouncedTerm;\n};\n\n// Virtual scrolling component for large lists\nexport const VirtualList = ({ \n  items, \n  itemHeight = 60, \n  containerHeight = 400,\n  renderItem,\n  className = '' \n}) => {\n  const [scrollTop, setScrollTop] = useState(0);\n  const [containerRef, setContainerRef] = useState(null);\n\n  const visibleStart = Math.floor(scrollTop / itemHeight);\n  const visibleEnd = Math.min(\n    visibleStart + Math.ceil(containerHeight / itemHeight) + 1,\n    items.length\n  );\n\n  const visibleItems = items.slice(visibleStart, visibleEnd);\n  const totalHeight = items.length * itemHeight;\n  const offsetY = visibleStart * itemHeight;\n\n  const handleScroll = (e) => {\n    setScrollTop(e.target.scrollTop);\n  };\n\n  return (\n    <div\n      ref={setContainerRef}\n      className={`overflow-auto ${className}`}\n      style={{ height: containerHeight }}\n      onScroll={handleScroll}\n    >\n      <div style={{ height: totalHeight, position: 'relative' }}>\n        <div style={{ transform: `translateY(${offsetY}px)` }}>\n          {visibleItems.map((item, index) => (\n            <div key={visibleStart + index} style={{ height: itemHeight }}>\n              {renderItem(item, visibleStart + index)}\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PerformanceIndicator;\n", {"ruleId": "549", "severity": 1, "message": "550", "line": 10, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 10, "endColumn": 21}, {"ruleId": "549", "severity": 1, "message": "553", "line": 10, "column": 23, "nodeType": "551", "messageId": "552", "endLine": 10, "endColumn": 34}, {"ruleId": "554", "severity": 1, "message": "555", "line": 219, "column": 6, "nodeType": "556", "endLine": 219, "endColumn": 8, "suggestions": "557"}, {"ruleId": "554", "severity": 1, "message": "558", "line": 284, "column": 6, "nodeType": "556", "endLine": 284, "endColumn": 33, "suggestions": "559"}, {"ruleId": "554", "severity": 1, "message": "560", "line": 291, "column": 6, "nodeType": "556", "endLine": 291, "endColumn": 25, "suggestions": "561"}, {"ruleId": "554", "severity": 1, "message": "562", "line": 37, "column": 6, "nodeType": "556", "endLine": 37, "endColumn": 8, "suggestions": "563"}, {"ruleId": "549", "severity": 1, "message": "564", "line": 1, "column": 35, "nodeType": "551", "messageId": "552", "endLine": 1, "endColumn": 41}, {"ruleId": "554", "severity": 1, "message": "565", "line": 81, "column": 6, "nodeType": "556", "endLine": 81, "endColumn": 8, "suggestions": "566"}, {"ruleId": "554", "severity": 1, "message": "567", "line": 93, "column": 6, "nodeType": "556", "endLine": 93, "endColumn": 8, "suggestions": "568"}, {"ruleId": "554", "severity": 1, "message": "569", "line": 65, "column": 8, "nodeType": "556", "endLine": 65, "endColumn": 32, "suggestions": "570"}, {"ruleId": "549", "severity": 1, "message": "571", "line": 5, "column": 18, "nodeType": "551", "messageId": "552", "endLine": 5, "endColumn": 33}, {"ruleId": "549", "severity": 1, "message": "572", "line": 15, "column": 53, "nodeType": "551", "messageId": "552", "endLine": 15, "endColumn": 57}, {"ruleId": "549", "severity": 1, "message": "573", "line": 15, "column": 59, "nodeType": "551", "messageId": "552", "endLine": 15, "endColumn": 65}, {"ruleId": "549", "severity": 1, "message": "574", "line": 15, "column": 67, "nodeType": "551", "messageId": "552", "endLine": 15, "endColumn": 74}, {"ruleId": "549", "severity": 1, "message": "575", "line": 16, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 16, "endColumn": 17}, {"ruleId": "549", "severity": 1, "message": "576", "line": 16, "column": 19, "nodeType": "551", "messageId": "552", "endLine": 16, "endColumn": 33}, {"ruleId": "549", "severity": 1, "message": "577", "line": 16, "column": 35, "nodeType": "551", "messageId": "552", "endLine": 16, "endColumn": 42}, {"ruleId": "549", "severity": 1, "message": "578", "line": 16, "column": 44, "nodeType": "551", "messageId": "552", "endLine": 16, "endColumn": 47}, {"ruleId": "549", "severity": 1, "message": "579", "line": 16, "column": 49, "nodeType": "551", "messageId": "552", "endLine": 16, "endColumn": 55}, {"ruleId": "549", "severity": 1, "message": "580", "line": 16, "column": 57, "nodeType": "551", "messageId": "552", "endLine": 16, "endColumn": 68}, {"ruleId": "549", "severity": 1, "message": "581", "line": 16, "column": 70, "nodeType": "551", "messageId": "552", "endLine": 16, "endColumn": 82}, {"ruleId": "549", "severity": 1, "message": "582", "line": 21, "column": 8, "nodeType": "551", "messageId": "552", "endLine": 21, "endColumn": 20}, {"ruleId": "549", "severity": 1, "message": "583", "line": 19, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 19, "endColumn": 16}, {"ruleId": "549", "severity": 1, "message": "584", "line": 20, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 20, "endColumn": 11}, {"ruleId": "549", "severity": 1, "message": "585", "line": 29, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 29, "endColumn": 11}, {"ruleId": "549", "severity": 1, "message": "586", "line": 30, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 30, "endColumn": 11}, {"ruleId": "549", "severity": 1, "message": "587", "line": 31, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 31, "endColumn": 18}, {"ruleId": "549", "severity": 1, "message": "588", "line": 32, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 32, "endColumn": 9}, {"ruleId": "549", "severity": 1, "message": "589", "line": 33, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 33, "endColumn": 13}, {"ruleId": "549", "severity": 1, "message": "590", "line": 34, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 34, "endColumn": 8}, {"ruleId": "549", "severity": 1, "message": "591", "line": 35, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 35, "endColumn": 13}, {"ruleId": "549", "severity": 1, "message": "592", "line": 36, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 36, "endColumn": 9}, {"ruleId": "549", "severity": 1, "message": "593", "line": 38, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 38, "endColumn": 14}, {"ruleId": "549", "severity": 1, "message": "578", "line": 39, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 39, "endColumn": 6}, {"ruleId": "549", "severity": 1, "message": "594", "line": 40, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 40, "endColumn": 18}, {"ruleId": "549", "severity": 1, "message": "595", "line": 41, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 41, "endColumn": 15}, {"ruleId": "549", "severity": 1, "message": "577", "line": 42, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 42, "endColumn": 10}, {"ruleId": "549", "severity": 1, "message": "596", "line": 43, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 43, "endColumn": 14}, {"ruleId": "554", "severity": 1, "message": "597", "line": 71, "column": 9, "nodeType": "598", "endLine": 75, "endColumn": 29}, {"ruleId": "549", "severity": 1, "message": "599", "line": 4, "column": 19, "nodeType": "551", "messageId": "552", "endLine": 4, "endColumn": 24}, {"ruleId": "554", "severity": 1, "message": "600", "line": 67, "column": 6, "nodeType": "556", "endLine": 67, "endColumn": 8, "suggestions": "601"}, {"ruleId": "549", "severity": 1, "message": "602", "line": 8, "column": 8, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 17}, {"ruleId": "549", "severity": 1, "message": "574", "line": 11, "column": 41, "nodeType": "551", "messageId": "552", "endLine": 11, "endColumn": 48}, {"ruleId": "549", "severity": 1, "message": "603", "line": 12, "column": 30, "nodeType": "551", "messageId": "552", "endLine": 12, "endColumn": 38}, {"ruleId": "549", "severity": 1, "message": "575", "line": 12, "column": 40, "nodeType": "551", "messageId": "552", "endLine": 12, "endColumn": 47}, {"ruleId": "549", "severity": 1, "message": "604", "line": 12, "column": 49, "nodeType": "551", "messageId": "552", "endLine": 12, "endColumn": 56}, {"ruleId": "549", "severity": 1, "message": "605", "line": 13, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 13, "endColumn": 20}, {"ruleId": "549", "severity": 1, "message": "606", "line": 50, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 50, "endColumn": 16}, {"ruleId": "554", "severity": 1, "message": "607", "line": 199, "column": 6, "nodeType": "556", "endLine": 199, "endColumn": 8, "suggestions": "608"}, {"ruleId": "549", "severity": 1, "message": "571", "line": 3, "column": 18, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 33}, {"ruleId": "549", "severity": 1, "message": "602", "line": 7, "column": 8, "nodeType": "551", "messageId": "552", "endLine": 7, "endColumn": 17}, {"ruleId": "549", "severity": 1, "message": "573", "line": 10, "column": 16, "nodeType": "551", "messageId": "552", "endLine": 10, "endColumn": 22}, {"ruleId": "549", "severity": 1, "message": "574", "line": 10, "column": 24, "nodeType": "551", "messageId": "552", "endLine": 10, "endColumn": 31}, {"ruleId": "549", "severity": 1, "message": "609", "line": 12, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 12, "endColumn": 31}, {"ruleId": "549", "severity": 1, "message": "610", "line": 19, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 19, "endColumn": 9}, {"ruleId": "549", "severity": 1, "message": "611", "line": 27, "column": 12, "nodeType": "551", "messageId": "552", "endLine": 27, "endColumn": 23}, {"ruleId": "549", "severity": 1, "message": "612", "line": 30, "column": 12, "nodeType": "551", "messageId": "552", "endLine": 30, "endColumn": 20}, {"ruleId": "554", "severity": 1, "message": "613", "line": 81, "column": 8, "nodeType": "556", "endLine": 81, "endColumn": 10, "suggestions": "614"}, {"ruleId": "554", "severity": 1, "message": "615", "line": 97, "column": 8, "nodeType": "556", "endLine": 97, "endColumn": 21, "suggestions": "616"}, {"ruleId": "549", "severity": 1, "message": "617", "line": 100, "column": 11, "nodeType": "551", "messageId": "552", "endLine": 100, "endColumn": 29}, {"ruleId": "549", "severity": 1, "message": "618", "line": 1, "column": 38, "nodeType": "551", "messageId": "552", "endLine": 1, "endColumn": 46}, {"ruleId": "549", "severity": 1, "message": "619", "line": 8, "column": 12, "nodeType": "551", "messageId": "552", "endLine": 8, "endColumn": 19}, {"ruleId": "554", "severity": 1, "message": "613", "line": 58, "column": 8, "nodeType": "556", "endLine": 58, "endColumn": 10, "suggestions": "620"}, {"ruleId": "554", "severity": 1, "message": "621", "line": 90, "column": 6, "nodeType": "556", "endLine": 90, "endColumn": 35, "suggestions": "622"}, {"ruleId": "549", "severity": 1, "message": "623", "line": 92, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 92, "endColumn": 26}, {"ruleId": "549", "severity": 1, "message": "602", "line": 11, "column": 8, "nodeType": "551", "messageId": "552", "endLine": 11, "endColumn": 17}, {"ruleId": "549", "severity": 1, "message": "590", "line": 21, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 21, "endColumn": 8}, {"ruleId": "549", "severity": 1, "message": "624", "line": 28, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 28, "endColumn": 17}, {"ruleId": "549", "severity": 1, "message": "625", "line": 33, "column": 19, "nodeType": "551", "messageId": "552", "endLine": 33, "endColumn": 29}, {"ruleId": "554", "severity": 1, "message": "626", "line": 117, "column": 6, "nodeType": "556", "endLine": 117, "endColumn": 8, "suggestions": "627"}, {"ruleId": "549", "severity": 1, "message": "628", "line": 1, "column": 38, "nodeType": "551", "messageId": "552", "endLine": 1, "endColumn": 44}, {"ruleId": "549", "severity": 1, "message": "629", "line": 35, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 35, "endColumn": 20}, {"ruleId": "554", "severity": 1, "message": "630", "line": 61, "column": 6, "nodeType": "556", "endLine": 61, "endColumn": 26, "suggestions": "631"}, {"ruleId": "554", "severity": 1, "message": "632", "line": 93, "column": 6, "nodeType": "556", "endLine": 93, "endColumn": 8, "suggestions": "633"}, {"ruleId": "554", "severity": 1, "message": "634", "line": 213, "column": 6, "nodeType": "556", "endLine": 213, "endColumn": 20, "suggestions": "635"}, {"ruleId": "554", "severity": 1, "message": "615", "line": 63, "column": 6, "nodeType": "556", "endLine": 63, "endColumn": 19, "suggestions": "636"}, {"ruleId": "554", "severity": 1, "message": "632", "line": 97, "column": 6, "nodeType": "556", "endLine": 97, "endColumn": 8, "suggestions": "637"}, {"ruleId": "549", "severity": 1, "message": "638", "line": 18, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 18, "endColumn": 14}, {"ruleId": "549", "severity": 1, "message": "639", "line": 23, "column": 8, "nodeType": "551", "messageId": "552", "endLine": 23, "endColumn": 14}, {"ruleId": "549", "severity": 1, "message": "573", "line": 26, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 26, "endColumn": 16}, {"ruleId": "549", "severity": 1, "message": "640", "line": 33, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 33, "endColumn": 17}, {"ruleId": "554", "severity": 1, "message": "641", "line": 41, "column": 38, "nodeType": "556", "endLine": 41, "endColumn": 40, "suggestions": "642"}, {"ruleId": "549", "severity": 1, "message": "643", "line": 2, "column": 8, "nodeType": "551", "messageId": "552", "endLine": 2, "endColumn": 13}, {"ruleId": "549", "severity": 1, "message": "644", "line": 74, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 74, "endColumn": 23}, {"ruleId": "549", "severity": 1, "message": "645", "line": 19, "column": 11, "nodeType": "551", "messageId": "552", "endLine": 19, "endColumn": 24}, {"ruleId": "646", "severity": 1, "message": "647", "line": 73, "column": 111, "nodeType": "648", "messageId": "649", "endLine": 73, "endColumn": 112, "suggestions": "650"}, {"ruleId": "646", "severity": 1, "message": "647", "line": 95, "column": 89, "nodeType": "648", "messageId": "649", "endLine": 95, "endColumn": 90, "suggestions": "651"}, {"ruleId": "554", "severity": 1, "message": "652", "line": 126, "column": 6, "nodeType": "556", "endLine": 126, "endColumn": 32, "suggestions": "653", "suppressions": "654"}, {"ruleId": "549", "severity": 1, "message": "655", "line": 1, "column": 17, "nodeType": "551", "messageId": "552", "endLine": 1, "endColumn": 26}, {"ruleId": "549", "severity": 1, "message": "656", "line": 1, "column": 28, "nodeType": "551", "messageId": "552", "endLine": 1, "endColumn": 36}, {"ruleId": "549", "severity": 1, "message": "657", "line": 20, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 20, "endColumn": 8}, {"ruleId": "549", "severity": 1, "message": "658", "line": 21, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 21, "endColumn": 11}, {"ruleId": "549", "severity": 1, "message": "584", "line": 22, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 22, "endColumn": 11}, {"ruleId": "554", "severity": 1, "message": "659", "line": 95, "column": 6, "nodeType": "556", "endLine": 95, "endColumn": 15, "suggestions": "660"}, {"ruleId": "549", "severity": 1, "message": "661", "line": 9, "column": 3, "nodeType": "551", "messageId": "552", "endLine": 9, "endColumn": 8}, {"ruleId": "549", "severity": 1, "message": "624", "line": 23, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 23, "endColumn": 17}, {"ruleId": "554", "severity": 1, "message": "662", "line": 37, "column": 6, "nodeType": "556", "endLine": 37, "endColumn": 8, "suggestions": "663"}, {"ruleId": "549", "severity": 1, "message": "664", "line": 42, "column": 5, "nodeType": "551", "messageId": "552", "endLine": 42, "endColumn": 15}, {"ruleId": "549", "severity": 1, "message": "665", "line": 7, "column": 9, "nodeType": "551", "messageId": "552", "endLine": 7, "endColumn": 14}, {"ruleId": "554", "severity": 1, "message": "666", "line": 32, "column": 6, "nodeType": "556", "endLine": 32, "endColumn": 15, "suggestions": "667"}, {"ruleId": "549", "severity": 1, "message": "668", "line": 3, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 3, "endColumn": 25}, {"ruleId": "549", "severity": 1, "message": "669", "line": 112, "column": 23, "nodeType": "551", "messageId": "552", "endLine": 112, "endColumn": 34}, {"ruleId": "549", "severity": 1, "message": "670", "line": 2, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 2, "endColumn": 16}, {"ruleId": "549", "severity": 1, "message": "671", "line": 197, "column": 10, "nodeType": "551", "messageId": "552", "endLine": 197, "endColumn": 22}, "no-unused-vars", "'HideLoading' is defined but never used.", "Identifier", "unusedVar", "'ShowLoading' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'getUserData' and 'navigate'. Either include them or remove the dependency array.", "ArrayExpression", ["672"], "React Hook useEffect has missing dependencies: 'dispatch', 'user?.isAdmin', 'user?.paymentRequired', and 'verifyPaymentStatus'. Either include them or remove the dependency array.", ["673"], "React Hook useEffect has a missing dependency: 'verifyPaymentStatus'. Either include it or remove the dependency array.", ["674"], "React Hook useEffect has a missing dependency: 'fetchAll'. Either include it or remove the dependency array.", ["675"], "'Select' is defined but never used.", "React Hook useEffect has missing dependencies: 'getExamData' and 'params.id'. Either include them or remove the dependency array.", ["676"], "React Hook useEffect has a missing dependency: 'getExamsData'. Either include it or remove the dependency array.", ["677"], "React Hook useEffect has a missing dependency: 'paymentInProgress'. Either include it or remove the dependency array.", ["678"], "'AnimatePresence' is defined but never used.", "'Card' is defined but never used.", "'Button' is defined but never used.", "'Loading' is defined but never used.", "'TbClock' is defined but never used.", "'TbQuestionMark' is defined but never used.", "'TbCheck' is defined but never used.", "'TbX' is defined but never used.", "'TbFlag' is defined but never used.", "'TbArrowLeft' is defined but never used.", "'TbArrowRight' is defined but never used.", "'QuizRenderer' is defined but never used.", "'FaChevronDown' is defined but never used.", "'FaSearch' is defined but never used.", "'TbSearch' is defined but never used.", "'TbFilter' is defined but never used.", "'TbSortAscending' is defined but never used.", "'TbPlay' is defined but never used.", "'TbDownload' is defined but never used.", "'TbEye' is defined but never used.", "'TbCalendar' is defined but never used.", "'TbUser' is defined but never used.", "'TbChevronUp' is defined but never used.", "'TbAlertTriangle' is defined but never used.", "'TbInfoCircle' is defined but never used.", "'TbSubtitles' is defined but never used.", "The 'allPossibleClasses' conditional could make the dependencies of useCallback Hook (at line 127) change on every render. Move it inside the useCallback callback. Alternatively, wrap the initialization of 'allPossibleClasses' in its own useMemo() Hook.", "VariableDeclarator", "'Modal' is defined but never used.", "React Hook useEffect has a missing dependency: 'getData'. Either include it or remove the dependency array.", ["679"], "'PageTitle' is defined but never used.", "'TbTrophy' is defined but never used.", "'TbUsers' is defined but never used.", "'BsBookFill' is defined but never used.", "'lgSize' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getData' and 'getExams'. Either include them or remove the dependency array.", ["680"], "'IoPersonCircleOutline' is defined but never used.", "'TbStar' is defined but never used.", "'userRanking' is assigned a value but never used.", "'isMobile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'dispatch' and 'getUserData'. Either include them or remove the dependency array.", ["681"], "React Hook useEffect has a missing dependency: 'getUserStats'. Either include it or remove the dependency array.", ["682"], "'formatMobileUserId' is assigned a value but never used.", "'Suspense' is defined but never used.", "'isAdmin' is assigned a value but never used.", ["683"], "React Hook useEffect has missing dependencies: 'getData' and 'pagination'. Either include them or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["684"], "'handleTableChange' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getUsersData'. Either include it or remove the dependency array.", ["685"], "'useRef' is defined but never used.", "'totalPages' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["686"], "React Hook useEffect has a missing dependency: 'getUserData'. Either include it or remove the dependency array.", ["687"], "React Hook useEffect has a missing dependency: 'form2'. Either include it or remove the dependency array.", ["688"], ["689"], ["690"], "'Rate' is defined but never used.", "'Image2' is defined but never used.", "'reviews' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getReviews'. Either include it or remove the dependency array.", ["691"], "'axios' is defined but never used.", "'handleKeyPress' is assigned a value but never used.", "'restoredLines' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\[.", "Literal", "unnecessaryEscape", ["692", "693"], ["694", "695"], "React Hook useEffect has a missing dependency: 'renderPDF'. Either include it or remove the dependency array.", ["696"], ["697"], "'useEffect' is defined but never used.", "'useState' is defined but never used.", "'FaEye' is defined but never used.", "'FaFilter' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMaterials'. Either include it or remove the dependency array.", ["698"], "'FaCog' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchGenerationHistory'. Either include it or remove the dependency array.", ["699"], "'needsLogin' is assigned a value but never used.", "'Title' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleAutoRefresh'. Either include it or remove the dependency array.", ["700"], "'validateSession' is defined but never used.", "'toggleTheme' is assigned a value but never used.", "'motion' is defined but never used.", "'containerRef' is assigned a value but never used.", {"desc": "701", "fix": "702"}, {"desc": "703", "fix": "704"}, {"desc": "705", "fix": "706"}, {"desc": "707", "fix": "708"}, {"desc": "709", "fix": "710"}, {"desc": "711", "fix": "712"}, {"desc": "713", "fix": "714"}, {"desc": "715", "fix": "716"}, {"desc": "717", "fix": "718"}, {"desc": "719", "fix": "720"}, {"desc": "721", "fix": "722"}, {"desc": "719", "fix": "723"}, {"desc": "724", "fix": "725"}, {"desc": "726", "fix": "727"}, {"desc": "728", "fix": "729"}, {"desc": "730", "fix": "731"}, {"desc": "732", "fix": "733"}, {"desc": "721", "fix": "734"}, {"desc": "730", "fix": "735"}, {"desc": "736", "fix": "737"}, {"messageId": "738", "fix": "739", "desc": "740"}, {"messageId": "741", "fix": "742", "desc": "743"}, {"messageId": "738", "fix": "744", "desc": "740"}, {"messageId": "741", "fix": "745", "desc": "743"}, {"desc": "746", "fix": "747"}, {"kind": "748", "justification": "749"}, {"desc": "750", "fix": "751"}, {"desc": "752", "fix": "753"}, {"desc": "754", "fix": "755"}, "Update the dependencies array to be: [getUserData, navigate]", {"range": "756", "text": "757"}, "Update the dependencies array to be: [dispatch, paymentVerificationNeeded, user?.isAdmin, user?.paymentRequired, verifyPaymentStatus]", {"range": "758", "text": "759"}, "Update the dependencies array to be: [user, activeRoute, verifyPaymentStatus]", {"range": "760", "text": "761"}, "Update the dependencies array to be: [fetchAll]", {"range": "762", "text": "763"}, "Update the dependencies array to be: [getExamData, params.id]", {"range": "764", "text": "765"}, "Update the dependencies array to be: [getExamsData]", {"range": "766", "text": "767"}, "Update the dependencies array to be: [user, subscriptionData, paymentInProgress]", {"range": "768", "text": "769"}, "Update the dependencies array to be: [getData]", {"range": "770", "text": "771"}, "Update the dependencies array to be: [getData, getExams]", {"range": "772", "text": "773"}, "Update the dependencies array to be: [dispatch, getUserData]", {"range": "774", "text": "775"}, "Update the dependencies array to be: [getUserStats, rankingData]", {"range": "776", "text": "777"}, {"range": "778", "text": "775"}, "Update the dependencies array to be: [filters, getData, pagination]", {"range": "779", "text": "780"}, "Update the dependencies array to be: [getUsersData]", {"range": "781", "text": "782"}, "Update the dependencies array to be: [currentPage, fetchQuestions, limit]", {"range": "783", "text": "784"}, "Update the dependencies array to be: [getUserData]", {"range": "785", "text": "786"}, "Update the dependencies array to be: [editQuestion, form2]", {"range": "787", "text": "788"}, {"range": "789", "text": "777"}, {"range": "790", "text": "786"}, "Update the dependencies array to be: [getReviews]", {"range": "791", "text": "792"}, "removeEscape", {"range": "793", "text": "749"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "794", "text": "795"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "796", "text": "749"}, {"range": "797", "text": "795"}, "Update the dependencies array to be: [modalIsOpen, documentUrl, renderPDF]", {"range": "798", "text": "799"}, "directive", "", "Update the dependencies array to be: [fetchMaterials, filters]", {"range": "800", "text": "801"}, "Update the dependencies array to be: [fetchGenerationHistory]", {"range": "802", "text": "803"}, "Update the dependencies array to be: [handleAutoRefresh, visible]", {"range": "804", "text": "805"}, [6801, 6803], "[get<PERSON><PERSON><PERSON><PERSON>, navigate]", [8612, 8639], "[dispatch, paymentVerificationNeeded, user?.isAdmin, user?.paymentRequired, verifyPaymentStatus]", [8800, 8819], "[user, activeRoute, verifyPaymentStatus]", [1156, 1158], "[fetchAll]", [2375, 2377], "[getExamData, params.id]", [2372, 2374], "[getExamsData]", [2446, 2470], "[user, subscriptionData, paymentInProgress]", [1901, 1903], "[getData]", [6617, 6619], "[getData, getExams]", [2517, 2519], "[dispatch, getUserData]", [2925, 2938], "[getUserStats, rankingData]", [1990, 1992], [2529, 2558], "[filters, getData, pagination]", [3320, 3322], "[getUsersData]", [2250, 2270], "[currentPage, fetchQuestions, limit]", [3018, 3020], "[getUserData]", [6129, 6143], "[editQuestion, form2]", [1961, 1974], [2889, 2891], [1443, 1445], "[getReviews]", [3500, 3501], [3500, 3500], "\\", [5011, 5012], [5011, 5011], [3978, 4004], "[modalIsOpen, documentUrl, renderPDF]", [2411, 2420], "[fetchMaterials, filters]", [1203, 1205], "[fetchGenerationHistory]", [1110, 1119], "[handleAutoRefresh, visible]"]