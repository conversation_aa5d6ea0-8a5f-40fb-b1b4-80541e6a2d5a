import { Form, message } from "antd";
import React from "react";
import './index.css';
import Logo from '../../../assets/logo.png';
import { useDispatch } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import { loginUser } from "../../../apicalls/users";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";

function Login() {
  const navigate = useNavigate()
  const dispatch = useDispatch();
  const onFinish = async (values) => {
    try {
      dispatch(ShowLoading());
      const response = await loginUser(values);
      dispatch(HideLoading());

      console.log('Login response:', response); // Debug log

      if (response.success) {
        message.success(response.message);
        localStorage.setItem("token", response.data);

        // Check if user is admin from the response
        if (response.response && response.response.isAdmin) {
          navigate("/admin/users");
        } else {
          navigate("/user/quiz");
        }
      } else {
        message.error(response.message);
      }
    } catch (error) {
      dispatch(HideLoading());
      console.error('Login error:', error); // Debug log
      message.error(error.message);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
          <div className="text-center mb-8">
            <img src={Logo} alt="brainwave-logo" className="h-16 mx-auto mb-4"/>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Welcome Back</h1>
            <p className="text-gray-600">Sign in to your account to continue</p>
          </div>

          <Form layout="vertical" onFinish={onFinish} className="space-y-6">
            <Form.Item name="email" label="Email" initialValue="">
              <input
                type="email"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                placeholder="Enter your email"
              />
            </Form.Item>

            <Form.Item name="password" label="Password" initialValue="">
              <input
                type="password"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                placeholder="Enter your password"
              />
            </Form.Item>

            <div className="space-y-4">
              <button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                Sign In
              </button>

              <div className="text-center">
                <Link
                  to="/register"
                  className="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200"
                >
                  Don't have an account? Sign up
                </Link>
              </div>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
}

export default Login;