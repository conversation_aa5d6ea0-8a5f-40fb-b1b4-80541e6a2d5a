import { message } from "antd";
import React, { useEffect, useState, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Flag from "../assets/tanzania-flag.png";
import Logo2 from "../assets/logo-2.png";
import { getUserInfo } from "../apicalls/users";
import { useDispatch, useSelector } from "react-redux";
import { SetUser } from "../redux/usersSlice.js";
import { useNavigate, useLocation } from "react-router-dom";
import { HideLoading, ShowLoading } from "../redux/loaderSlice";
import { checkPaymentStatus } from "../apicalls/payment.js";
import "./ProtectedRoute.css";
import { SetSubscription } from "../redux/subscriptionSlice.js";
import { setPaymentVerificationNeeded } from "../redux/paymentSlice.js";
import { useTheme } from "../contexts/ThemeContext";

function ProtectedRoute({ children }) {
  const { user } = useSelector((state) => state.user);
  const [menu, setMenu] = useState([]);
  const [collapsed, setCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isPaymentPending, setIsPaymentPending] = useState(false);
  const intervalRef = useRef(null);
  const { subscriptionData } = useSelector((state) => state.subscription);
  const { paymentVerificationNeeded } = useSelector((state) => state.payment);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const activeRoute = location.pathname;

  const userMenu = [
    {
      title: "Hub",
      paths: ["/user/hub"],
      icon: <i className="ri-apps-2-line"></i>,
      onClick: () => navigate("/user/hub"),
    },
    {
      title: "Quiz",
      paths: ["/user/quiz", "/user/write-exam"],
      icon: <i className="ri-pencil-line"></i>,
      onClick: () => navigate("/user/quiz"),
    },

    {
      title: "Reports",
      paths: ["/user/reports"],
      icon: <i className="ri-bar-chart-line"></i>,
      onClick: () => navigate("/user/reports"),
    },
    {
      title: "Ranking",
      paths: ["/user/ranking"],
      icon: <i className="ri-trophy-line"></i>,
      onClick: () => navigate("/user/ranking"),
    },
    {
      title: "Study Material",
      paths: ["/user/study-material"],
      icon: <i className="ri-book-open-line"></i>,
      onClick: () => navigate("/user/study-material"),
    },
    {
      title: "About Us",
      paths: ["/user/about-us"],
      icon: <i className="ri-information-line"></i>,
      onClick: () => navigate("/user/about-us"),
    },
    {
      title: "Ask AI",
      paths: ["/user/chat"],
      icon: <i className="ri-chat-smile-2-line"></i>,
      onClick: () => navigate("/user/chat"),
    },
    {
      title: "Plans",
      paths: ["/user/plans"],
      icon: <i className="ri-calendar-check-line"></i>,
      onClick: () => navigate("/user/plans"),
    },
    {
      title: "Forum",
      paths: ["/forum"],
      icon: <i className="ri-discuss-line"></i>,
      onClick: () => navigate("/forum"),
    },
    {
      title: "Profile",
      paths: ["/profile"],
      icon: <i className="ri-user-line"></i>,
      onClick: () => navigate("/profile"),
    },
    {
      title: "Logout",
      paths: ["/logout"],
      icon: <i className="ri-logout-box-line"></i>,
      onClick: () => {
        localStorage.removeItem("token");
        navigate("/login");
      },
    },
  ];

  const adminMenu = [
    {
      title: "Users",
      paths: ["/admin/users", "/admin/users/add"],
      icon: <i className="ri-file-list-line"></i>,
      onClick: () => navigate("/admin/users"),
    },
    {
      title: "Exams",
      paths: ["/admin/exams", "/admin/exams/add"],
      icon: <i className="ri-file-list-line"></i>,
      onClick: () => navigate("/admin/exams"),
    },
    {
      title: "AI Questions",
      paths: ["/admin/ai-questions"],
      icon: <i className="ri-robot-line"></i>,
      onClick: () => navigate("/admin/ai-questions"),
    },
    {
      title: "Study Materials",
      paths: ["/admin/study-materials"],
      icon: <i className="ri-book-line"></i>,
      onClick: () => navigate("/admin/study-materials"),
    },
    {
      title: "Reports",
      paths: ["/admin/reports"],
      icon: <i className="ri-bar-chart-line"></i>,
      onClick: () => navigate("/admin/reports"),
    },
    {
      title: "Forum",
      paths: ["/forum"],
      icon: <i className="ri-discuss-line"></i>,
      onClick: () => navigate("/forum"),
    },
    {
      title: "Profile",
      paths: ["/profile"],
      icon: <i className="ri-user-line"></i>,
      onClick: () => navigate("/profile"),
    },
    {
      title: "Announcements",
      paths: ["/admin/announcements"],
      icon: <i className="ri-notification-line"></i>,
      onClick: () => navigate("/admin/announcements"),
    },
    {
      title: "Logout",
      paths: ["/logout"],
      icon: <i className="ri-logout-box-line"></i>,
      onClick: () => {
        localStorage.removeItem("token");
        navigate("/login");
      },
    },
  ];

  const getUserData = async () => {
    try {
      console.log('Getting user data...'); // Debug log
      const response = await getUserInfo();
      console.log('User data response:', response); // Debug log

      if (response.success) {
        dispatch(SetUser(response.data));
        if (response.data.isAdmin) {
          setMenu(adminMenu);
        } else {
          setMenu(userMenu);
        }
      } else {
        console.error('Failed to get user data:', response.message); // Debug log
        message.error(response.message);
        navigate("/login");
      }
    } catch (error) {
      console.error('Error getting user data:', error); // Debug log
      navigate("/login");
      message.error(error.message);
    }
  };

  useEffect(() => {
    // Function to handle resizing
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      setCollapsed(window.innerWidth < 768);
    };

    // Add resize event listener
    window.addEventListener("resize", handleResize);

    if (window.innerWidth < 768) {
      setIsMobile(true);
      setCollapsed(true);
    }

    // Check for token and navigate
    const token = localStorage.getItem("token");
    console.log('Token check:', token ? 'Token exists' : 'No token found'); // Debug log

    if (token) {
      getUserData();
    } else {
      console.log('No token, redirecting to login'); // Debug log
      navigate("/login");
    }

    // Cleanup the event listener when the component is unmounted
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const getIsActiveOrNot = (paths) => {
    if (paths.includes(activeRoute)) {
      return true;
    } else {
      if (
        activeRoute.includes("/admin/exams/edit") &&
        paths.includes("/admin/exams")
      ) {
        return true;
      }
      if (
        activeRoute.includes("/user/write-exam") &&
        paths.includes("/user/write-exam")
      ) {
        return true;
      }
    }
    return false;
  };

  useEffect(() => {
    if (isPaymentPending && !['/plans', '/profile'].includes(activeRoute)) {
      navigate('/user/plans');
    }
  }, [isPaymentPending, activeRoute, navigate]);

  const verifyPaymentStatus = async () => {
    try {
      const data = await checkPaymentStatus();
      console.log("Payment Status:", data);
      if (data?.error || data?.paymentStatus !== 'paid') {
        if (subscriptionData !== null) {
          dispatch(SetSubscription(null));
        }
        setIsPaymentPending(true);
      }
      else {
        setIsPaymentPending(false);
        dispatch(SetSubscription(data));
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      }
    } catch (error) {
      console.log("Error checking payment status:", error);
      dispatch(SetSubscription(null));
      setIsPaymentPending(true);
    }
  };

  useEffect(() => {
    if (user?.paymentRequired && !user?.isAdmin) {
      console.log("Effect Runing 2222222...");

      if (paymentVerificationNeeded) {
        console.log('Inside timer in effect 2....');
        intervalRef.current = setInterval(() => {
          console.log('Timer in action...');
          verifyPaymentStatus();
        }, 15000);
        dispatch(setPaymentVerificationNeeded(false));
      }
    }
  }, [paymentVerificationNeeded]);

  useEffect(() => {
    if (user?.paymentRequired && !user?.isAdmin) {
      console.log("Effect Runing...");
      verifyPaymentStatus();
    }
  }, [user, activeRoute]);


  const getButtonClass = (title) => {
    // Exclude "Plans" and "Profile" buttons from the "button-disabled" class
    if (!user.paymentRequired || title === "Plans" || title === "Profile" || title === "Logout") {
      return ""; // No class applied
    }

    return subscriptionData?.paymentStatus !== "paid" && user?.paymentRequired
      ? "button-disabled"
      : "";
  };


  const { isDarkMode, toggleTheme } = useTheme();

  return (
    <div className="layout-modern min-h-screen flex">
      {/* Modern Sidebar */}
      <AnimatePresence>
        {(!collapsed || !isMobile) && (
          <motion.div
            initial={{ x: -300, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -300, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className={`sidebar-modern ${isMobile ? "fixed inset-y-0 left-0 z-50 w-64" : "w-64"} ${
              collapsed && isMobile ? "hidden" : ""
            }`}
          >
            <div className="flex flex-col h-full">
              {/* Sidebar Header */}
              <div className="p-6 border-b border-white/10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <img
                      src={Logo2}
                      alt="BrainWave Logo"
                      className="w-10 h-10 rounded-lg shadow-lg"
                    />
                    {!collapsed && (
                      <div className="text-white font-bold text-xl">
                        BRAIN<span className="text-blue-300">WAVE</span>
                      </div>
                    )}
                  </div>
                  {isMobile && (
                    <button
                      onClick={() => setCollapsed(true)}
                      className="text-white/80 hover:text-white p-2 rounded-lg hover:bg-white/10 transition-colors"
                    >
                      <i className="ri-close-line text-xl"></i>
                    </button>
                  )}
                </div>
              </div>

              {/* Navigation Menu */}
              <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
                {menu.map((item, index) => {
                  const isActive = getIsActiveOrNot(item.paths);
                  const isDisabled = getButtonClass(item.title) === "button-disabled";

                  return (
                    <motion.div
                      key={index}
                      whileHover={{ x: isDisabled ? 0 : 4 }}
                      whileTap={{ scale: isDisabled ? 1 : 0.95 }}
                    >
                      <button
                        className={`sidebar-item w-full text-left ${
                          isActive ? "sidebar-item-active" : ""
                        } ${isDisabled ? "opacity-50 cursor-not-allowed" : ""}`}
                        onClick={!isDisabled ? item.onClick : undefined}
                        disabled={isDisabled}
                      >
                        <span className="text-xl mr-3">{item.icon}</span>
                        {!collapsed && (
                          <span className="font-medium">{item.title}</span>
                        )}
                        {isActive && !collapsed && (
                          <motion.div
                            layoutId="activeIndicator"
                            className="ml-auto w-2 h-2 bg-white rounded-full"
                          />
                        )}
                      </button>
                    </motion.div>
                  );
                })}
              </nav>

              {/* Sidebar Footer */}
              {!collapsed && (
                <div className="p-4 border-t border-white/10">
                  <div className="flex items-center space-x-3">
                    <img
                      src={Flag}
                      alt="tanzania-flag"
                      className="w-8 h-6 rounded"
                    />
                    <div className="text-white/80 text-sm">
                      <div className="font-medium">{user?.name}</div>
                      <div className="text-xs text-white/60">
                        {user?.isAdmin ? "Administrator" : "Student"}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      {/* Mobile Overlay */}
      {isMobile && !collapsed && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 z-40"
          onClick={() => setCollapsed(true)}
        />
      )}

      {/* Main Content Area */}
      <div className={`flex-1 flex flex-col min-h-screen ${isMobile ? "" : collapsed ? "ml-0" : "ml-0"}`}>
        {/* Modern Header */}
        <motion.header
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="nav-modern bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-30 shadow-sm"
        >
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              {/* Left Section */}
              <div className="flex items-center space-x-4">
                {/* Mobile Menu Button */}
                {isMobile && (
                  <button
                    onClick={() => setCollapsed(false)}
                    className="p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
                  >
                    <i className="ri-menu-line text-xl"></i>
                  </button>
                )}

                {/* Desktop Collapse Button */}
                {!isMobile && (
                  <button
                    onClick={() => setCollapsed(!collapsed)}
                    className="p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
                  >
                    <i className={`ri-${collapsed ? 'menu' : 'close'}-line text-xl`}></i>
                  </button>
                )}

                {/* Logo for mobile when sidebar is collapsed */}
                {isMobile && collapsed && (
                  <div className="flex items-center space-x-2">
                    <img src={Logo2} alt="BrainWave Logo" className="w-10 h-10 rounded-lg" />
                    <span className="font-bold text-gray-900 text-lg">
                      BRAIN<span className="text-primary-600">WAVE</span>
                    </span>
                  </div>
                )}

                {/* Always show logo and title on quiz pages */}
                {!isMobile && (
                  <div className="flex items-center space-x-3">
                    <img src={Logo2} alt="BrainWave Logo" className="w-10 h-10 rounded-lg" />
                    <span className="font-bold text-gray-900 text-lg">
                      BRAIN<span className="text-primary-600">WAVE</span>
                    </span>
                  </div>
                )}
              </div>

              {/* Right Section */}
              <div className="flex items-center space-x-4">
                {/* Dark Mode Toggle */}
                <button
                  onClick={toggleTheme}
                  className="p-2 rounded-lg text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-colors"
                >
                  <i className={`ri-${isDarkMode ? 'sun' : 'moon'}-line text-xl`}></i>
                </button>

                {/* User Profile */}
                <div className="flex items-center space-x-4">
                  <div className="hidden sm:block text-right">
                    <div className="text-sm font-semibold text-gray-900">{user?.name}</div>
                    <div className="text-xs text-gray-600 font-medium">
                      {user?.isAdmin ? "Administrator" : `Class ${user?.class} Student`}
                    </div>
                  </div>
                  <div className="w-10 h-10 bg-gradient-to-br from-primary-100 to-primary-200 rounded-full flex items-center justify-center border-2 border-primary-300">
                    <span className="text-primary-700 font-bold text-sm">
                      {user?.name?.charAt(0)?.toUpperCase()}
                    </span>
                  </div>
                  <img src={Flag} alt="Tanzania Flag" className="w-8 h-6 rounded border border-gray-200 shadow-sm" />
                </div>
              </div>
            </div>
          </div>
        </motion.header>

        {/* Page Content */}
        <main className="flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-blue-50">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="h-full"
          >
            {children}
          </motion.div>
        </main>
      </div>
    </div>
  );
}

export default ProtectedRoute;
