/* ===== MODERN RESPONSIVE HOME PAGE ===== */

.Home {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    min-height: 100vh;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
}

/* ===== MODERN NAVIGATION ===== */
.nav-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.container-modern {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.nav-item {
    color: #4b5563;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 0.875rem;
}

.nav-item:hover {
    color: #007BFF;
    background: rgba(0, 123, 255, 0.05);
}

/* ===== TYPOGRAPHY ===== */
.heading-1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    color: #1f2937;
    margin-bottom: 1.5rem;
}

.text-gradient {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== HERO SECTION ===== */
.hero-section {
    padding: 6rem 1rem 4rem;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

/* ===== CTA BUTTONS ===== */
.cta-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    text-decoration: none;
    border: none;
    cursor: pointer;
    white-space: nowrap;
}

.btn-primary {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
}

.btn-secondary {
    background: white;
    color: #007BFF;
    border: 2px solid #007BFF;
}

.btn-secondary:hover {
    background: #007BFF;
    color: white;
    transform: translateY(-2px);
}

/* ===== TRUST INDICATORS ===== */
.trust-indicators {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-top: 2rem;
    color: #6b7280;
    font-size: 0.875rem;
}

.trust-indicators > div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* ===== HERO IMAGE ===== */
.hero-image {
    position: relative;
    max-width: 100%;
    height: auto;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* ===== MEDIA QUERIES ===== */
@media (max-width: 768px) {
    .hero-grid, .about-grid {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .trust-indicators {
        justify-content: center;
        flex-wrap: wrap;
        gap: 1rem;
    }
}

/* Add other section styles such as stats, about, reviews, contact, and footer using the same structure */
