/* ===== MODERN RESPONSIVE HOME PAGE ===== */

.Home {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    min-height: 100vh;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
}

/* ===== MODERN NAVIGATION ===== */
.nav-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.container-modern {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.nav-item {
    color: #4b5563;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 0.875rem;
}

.nav-item:hover {
    color: #007BFF;
    background: rgba(0, 123, 255, 0.05);
}

/* ===== RESPONSIVE TYPOGRAPHY ===== */
.heading-1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    color: #1f2937;
    margin-bottom: 1.5rem;
}

.text-gradient {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== RESPONSIVE SECTIONS ===== */
.hero-section {
    padding: 6rem 1rem 4rem;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

/* ===== MOBILE RESPONSIVE DESIGN ===== */
@media (max-width: 480px) {
    .container-modern {
        padding: 0 0.75rem;
    }

    .hero-section {
        padding: 5rem 0.75rem 3rem;
    }

    .hero-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .heading-1 {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .nav-item {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    /* Stack buttons vertically on mobile */
    .cta-buttons {
        flex-direction: column !important;
        gap: 0.75rem !important;
    }

    .cta-buttons .btn {
        width: 100% !important;
        justify-content: center !important;
    }

    /* Hide some trust indicators on very small screens */
    .trust-indicators {
        flex-wrap: wrap !important;
        justify-content: center !important;
        gap: 0.75rem !important;
    }

    .trust-indicators > div:nth-child(n+3) {
        display: none;
    }
}

@media (min-width: 481px) and (max-width: 768px) {
    .container-modern {
        padding: 0 1rem;
    }

    .hero-section {
        padding: 5.5rem 1rem 3.5rem;
    }

    .hero-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .heading-1 {
        font-size: 2.5rem;
        margin-bottom: 1.25rem;
    }

    .cta-buttons {
        flex-direction: row !important;
        justify-content: center !important;
        gap: 1rem !important;
    }

    .trust-indicators {
        justify-content: center !important;
        flex-wrap: wrap !important;
        gap: 1rem !important;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .hero-grid {
        gap: 3rem;
    }

    .heading-1 {
        font-size: 3rem;
    }
}

@media (min-width: 1025px) {
    .hero-grid {
        gap: 4rem;
    }
}
    

/* ===== RESPONSIVE BUTTONS ===== */
.cta-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    text-decoration: none;
    border: none;
    cursor: pointer;
    white-space: nowrap;
}

.btn-primary {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
}

.btn-secondary {
    background: white;
    color: #007BFF;
    border: 2px solid #007BFF;
}

.btn-secondary:hover {
    background: #007BFF;
    color: white;
    transform: translateY(-2px);
}

/* ===== TRUST INDICATORS ===== */
.trust-indicators {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-top: 2rem;
    color: #6b7280;
    font-size: 0.875rem;
}

.trust-indicators > div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* ===== HERO IMAGE ===== */
.hero-image {
    position: relative;
    max-width: 100%;
    height: auto;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* ===== RESPONSIVE HERO IMAGE ===== */
@media (max-width: 480px) {
    .hero-image {
        margin-top: 2rem;
    }

    .hero-image img {
        border-radius: 0.75rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    }
}

@media (min-width: 481px) and (max-width: 768px) {
    .hero-image {
        margin-top: 2rem;
        max-width: 80%;
        margin-left: auto;
        margin-right: auto;
    }
}

/* ===== STATS SECTION ===== */
.stats-section {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    padding: 4rem 1rem;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.stat-number {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    line-height: 1;
}

.stat-text {
    font-size: 0.875rem;
    opacity: 0.9;
    font-weight: 500;
}

/* Responsive stats */
@media (max-width: 480px) {
    .stats-section {
        padding: 3rem 0.75rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 1.75rem;
    }

    .stat-text {
        font-size: 0.75rem;
    }
}

@media (min-width: 481px) and (max-width: 768px) {
    .stats-section {
        padding: 3.5rem 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

/* ===== ABOUT SECTION ===== */
.about-section {
    padding: 6rem 1rem;
    background: white;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.about-content {
    order: 2;
}

.about-image {
    order: 1;
}

.about-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.about-text {
    font-size: 1.125rem;
    color: #6b7280;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Responsive about section */
@media (max-width: 480px) {
    .about-section {
        padding: 4rem 0.75rem;
    }

    .about-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .about-content {
        order: 1;
    }

    .about-image {
        order: 2;
    }

    .about-title {
        font-size: 1.75rem;
        margin-bottom: 1rem;
    }

    .about-text {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }
}

@media (min-width: 481px) and (max-width: 768px) {
    .about-section {
        padding: 5rem 1rem;
    }

    .about-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .about-content {
        order: 1;
    }

    .about-image {
        order: 2;
        max-width: 80%;
        margin: 0 auto;
    }

    .about-title {
        font-size: 2.25rem;
    }
}
            }
        }
    }

/* ===== REVIEWS SECTION ===== */
.reviews-section {
    padding: 6rem 1rem;
    background: #f8fafc;
}

.reviews-container {
    max-width: 1200px;
    margin: 0 auto;
}

.reviews-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    text-align: center;
    color: #1f2937;
    margin-bottom: 3rem;
}

.reviews-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.review-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.review-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.review-rating {
    margin-bottom: 1rem;
}

.review-text {
    flex: 1;
    font-size: 0.875rem;
    line-height: 1.6;
    color: #6b7280;
    margin-bottom: 1.5rem;
}

.review-divider {
    height: 1px;
    background: #e5e7eb;
    margin-bottom: 1rem;
}

.review-author {
    font-weight: 600;
    color: #007BFF;
    font-size: 0.875rem;
}

/* Responsive reviews */
@media (max-width: 480px) {
    .reviews-section {
        padding: 4rem 0.75rem;
    }

    .reviews-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .review-card {
        padding: 1.5rem;
    }

    .reviews-title {
        font-size: 1.75rem;
        margin-bottom: 2rem;
    }
}

@media (min-width: 481px) and (max-width: 768px) {
    .reviews-section {
        padding: 5rem 1rem;
    }

    .reviews-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .reviews-title {
        font-size: 2.25rem;
        margin-bottom: 2.5rem;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .reviews-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}
}

/* ===== CONTACT SECTION ===== */
.contact-section {
    padding: 6rem 1rem;
    background: white;
}

.contact-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.contact-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 1rem;
}

.contact-subtitle {
    font-size: 1.125rem;
    color: #6b7280;
    margin-bottom: 3rem;
}

.contact-form {
    display: grid;
    gap: 1.5rem;
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-input {
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #007BFF;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-textarea {
    min-height: 120px;
    resize: vertical;
}

.form-submit {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 1rem;
}

.form-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
}

/* Responsive contact */
@media (max-width: 480px) {
    .contact-section {
        padding: 4rem 0.75rem;
    }

    .contact-title {
        font-size: 1.75rem;
    }

    .contact-subtitle {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .contact-form {
        gap: 1rem;
    }
}

/* ===== FOOTER ===== */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 1rem 2rem;
    text-align: center;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
}

.footer-text {
    font-size: 0.875rem;
    color: #9ca3af;
}

/* Remove old responsive code */
/* ===== MODERN RESPONSIVE HOME PAGE COMPLETE ===== */

/* All responsive styles are now handled above with mobile-first approach */
/* Clean, modern design with proper breakpoints and consistent spacing */


  