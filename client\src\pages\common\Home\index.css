/* ===== RESPONSIVE HOME PAGE ===== */

.Home {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    min-height: 100vh;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
}

/* ===== NAVIGATION ===== */
.nav-header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    width: 100%;
}

/* Navigation Items */
.nav-item {
    color: #1f2937;
    font-weight: 600;
    padding: 0.75rem 1.25rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 1rem;
    text-decoration: none !important;
    display: inline-block;
    font-family: inherit;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.nav-item:hover {
    color: #007BFF !important;
    background: rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
    text-decoration: none !important;
}

/* Logo Styling */
.logo-text {
    font-size: 1.75rem;
    font-weight: 800;
    color: #1f2937;
}

.logo-accent {
    color: #007BFF;
}

/* Mobile Navigation */
.mobile-nav {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    margin-top: 1rem;
    padding: 1.5rem;
    border: 1px solid rgba(0, 123, 255, 0.1);
}

.mobile-nav .nav-item {
    display: block;
    width: 100%;
    text-align: center;
    padding: 1.25rem 1rem;
    margin-bottom: 0.75rem;
    border-radius: 0.75rem;
    font-size: 1.125rem;
    font-weight: 600;
    background: rgba(0, 123, 255, 0.05);
    border: 1px solid rgba(0, 123, 255, 0.1);
    color: #1f2937;
}

.mobile-nav .nav-item:hover {
    background: rgba(0, 123, 255, 0.1);
    color: #007BFF;
    transform: translateY(-2px);
}

.mobile-nav .nav-item:last-child {
    margin-bottom: 0;
}

/* ===== TYPOGRAPHY ===== */
.hero-title {
    font-size: clamp(1.75rem, 6vw, 4rem);
    font-weight: 800;
    line-height: 1.2;
    color: #1f2937;
    margin-bottom: 1.5rem;
    text-align: left;
}

.hero-subtitle {
    font-size: clamp(1.125rem, 3vw, 1.375rem);
    line-height: 1.6;
    color: #374151;
    margin-bottom: 2rem;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.text-gradient {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: inline-block;
}

/* ===== HERO SECTION ===== */
.hero-section {
    padding: 8rem 1rem 4rem;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.hero-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.hero-content {
    padding: 2rem 0;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background: rgba(0, 123, 255, 0.1);
    color: #007BFF;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 2rem;
    border: 1px solid rgba(0, 123, 255, 0.2);
}

/* ===== BUTTONS ===== */
.cta-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
    align-items: center;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-decoration: none !important;
    border: none;
    cursor: pointer;
    white-space: nowrap;
    min-height: 52px;
    min-width: 160px;
    position: relative;
    overflow: hidden;
    font-family: inherit;
    line-height: 1.2;
}

.btn-primary {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    border: 2px solid transparent;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
    color: white;
}

.btn-secondary {
    background: white;
    color: #007BFF;
    border: 2px solid #007BFF;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background: #007BFF;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

/* Button Icons */
.btn svg {
    width: 1.25rem;
    height: 1.25rem;
}

/* ===== TRUST INDICATORS ===== */
.trust-indicators {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-top: 2.5rem;
    flex-wrap: wrap;
}

.trust-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    background: rgba(0, 123, 255, 0.05);
    border-radius: 2rem;
    border: 1px solid rgba(0, 123, 255, 0.1);
}

.trust-indicator svg {
    width: 1.25rem;
    height: 1.25rem;
}

/* ===== HERO IMAGE ===== */
.hero-image {
    position: relative;
    max-width: 100%;
    height: auto;
    padding: 1rem;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 1.5rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    transition: transform 0.3s ease;
}

.hero-image:hover img {
    transform: scale(1.02);
}

/* Floating elements */
.floating-element {
    position: absolute;
    background: white;
    border-radius: 1rem;
    padding: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 10;
}

.floating-element svg {
    width: 2rem;
    height: 2rem;
}

/* ===== STATS SECTION ===== */
.stats-section {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    padding: 4rem 1rem;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.stat-number {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    line-height: 1;
}

.stat-text {
    font-size: 0.875rem;
    opacity: 0.9;
    font-weight: 500;
}

/* ===== ABOUT SECTION ===== */
.about-section {
    padding: 6rem 1rem;
    background: white;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.about-content {
    order: 2;
}

.about-image {
    order: 1;
}

.about-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.about-text {
    font-size: 1.125rem;
    color: #6b7280;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* ===== REVIEWS SECTION ===== */
.reviews-section {
    padding: 6rem 1rem;
    background: #f8fafc;
}

.reviews-container {
    max-width: 1200px;
    margin: 0 auto;
}

.reviews-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    text-align: center;
    color: #1f2937;
    margin-bottom: 3rem;
}

.reviews-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.review-card {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.review-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.review-rating {
    margin-bottom: 1rem;
}

.review-text {
    flex: 1;
    font-size: 0.875rem;
    line-height: 1.6;
    color: #6b7280;
    margin-bottom: 1.5rem;
}

.review-divider {
    height: 1px;
    background: #e5e7eb;
    margin-bottom: 1rem;
}

.review-author {
    font-weight: 600;
    color: #007BFF;
    font-size: 0.875rem;
}

/* ===== CONTACT SECTION ===== */
.contact-section {
    padding: 6rem 1rem;
    background: white;
}

.contact-container {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.contact-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 1rem;
}

.contact-subtitle {
    font-size: 1.125rem;
    color: #6b7280;
    margin-bottom: 3rem;
}

.contact-form {
    display: grid;
    gap: 1.5rem;
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-input {
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #007BFF;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-textarea {
    min-height: 120px;
    resize: vertical;
}

.form-submit {
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 1rem;
}

.form-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
}

/* ===== FOOTER ===== */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 1rem 2rem;
    text-align: center;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
}

.footer-text {
    font-size: 0.875rem;
    color: #9ca3af;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
        max-width: 100%;
    }

    /* Navigation */
    .nav-header {
        padding: 0.5rem 0;
    }

    .nav-item {
        font-size: 1.1rem;
        padding: 1rem 1.5rem;
        margin-bottom: 0.5rem;
        border-radius: 0.75rem;
        background: rgba(0, 123, 255, 0.05);
        border: 1px solid rgba(0, 123, 255, 0.1);
    }

    .logo-text {
        font-size: 1.5rem;
    }

    /* Hero Section */
    .hero-section {
        padding: 6rem 1rem 4rem;
        min-height: auto;
        text-align: center;
    }

    .hero-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-content {
        order: 1;
        padding: 1rem 0;
    }

    .hero-image {
        order: 2;
        padding: 0.5rem;
        margin-top: 2rem;
    }

    .hero-title {
        font-size: 2.25rem;
        line-height: 1.2;
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .hero-subtitle {
        font-size: 1.125rem;
        line-height: 1.6;
        margin-bottom: 2rem;
        text-align: center;
        color: #4b5563;
    }

    .hero-badge {
        font-size: 0.875rem;
        padding: 0.75rem 1.25rem;
        margin-bottom: 1.5rem;
    }

    /* Buttons */
    .cta-buttons {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
        width: 100%;
    }

    .btn {
        width: 100%;
        max-width: 280px;
        padding: 1.25rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
    }

    /* Trust Indicators */
    .trust-indicators {
        justify-content: center;
        gap: 1rem;
        margin-top: 2rem;
        flex-wrap: wrap;
    }

    .trust-indicator {
        font-size: 0.875rem;
        padding: 0.75rem 1rem;
    }

    /* Hide third indicator on very small screens */
    .trust-indicator:nth-child(3) {
        display: none;
    }

    /* Stats Section */
    .stats-section {
        padding: 3rem 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-text {
        font-size: 0.875rem;
    }

    /* About Section */
    .about-section {
        padding: 4rem 1rem;
    }

    .about-grid {
        grid-template-columns: 1fr;
        gap: 2.5rem;
        text-align: center;
    }

    .about-content {
        order: 1;
    }

    .about-image {
        order: 2;
    }

    .about-title {
        font-size: 2rem;
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .about-text {
        font-size: 1.125rem;
        line-height: 1.7;
        text-align: center;
        margin-bottom: 2rem;
    }

    /* Reviews Section */
    .reviews-section {
        padding: 4rem 1rem;
    }

    .reviews-title {
        font-size: 2rem;
        margin-bottom: 2.5rem;
        text-align: center;
    }

    .reviews-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .review-card {
        padding: 1.5rem;
        text-align: left;
    }

    .review-text {
        font-size: 1rem;
        line-height: 1.6;
    }

    /* Contact Section */
    .contact-section {
        padding: 4rem 1rem;
    }

    .contact-title {
        font-size: 2rem;
        text-align: center;
    }

    .contact-subtitle {
        font-size: 1.125rem;
        margin-bottom: 2.5rem;
        text-align: center;
    }

    .contact-form {
        gap: 1.25rem;
    }

    .form-input {
        font-size: 1rem;
        padding: 1rem 1.25rem;
    }

    .form-submit {
        padding: 1.25rem 2rem;
        font-size: 1.1rem;
    }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .container {
        padding: 0 1.5rem;
    }

    /* Navigation */
    .nav-item {
        font-size: 1rem;
        padding: 0.875rem 1.25rem;
    }

    .logo-text {
        font-size: 1.625rem;
    }

    /* Hero Section */
    .hero-section {
        padding: 7rem 1.5rem 4rem;
        text-align: center;
    }

    .hero-grid {
        grid-template-columns: 1fr;
        gap: 3.5rem;
        text-align: center;
    }

    .hero-content {
        order: 1;
    }

    .hero-image {
        order: 2;
        max-width: 85%;
        margin: 0 auto;
    }

    .hero-title {
        font-size: 2.75rem;
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .hero-subtitle {
        font-size: 1.25rem;
        margin-bottom: 2rem;
        text-align: center;
    }

    /* Buttons */
    .cta-buttons {
        flex-direction: row;
        justify-content: center;
        gap: 1.25rem;
        flex-wrap: wrap;
    }

    .btn {
        min-width: 160px;
        padding: 1.125rem 2rem;
        font-size: 1rem;
    }

    /* Trust Indicators */
    .trust-indicators {
        justify-content: center;
        gap: 1.5rem;
        flex-wrap: wrap;
    }

    /* Stats Section */
    .stats-section {
        padding: 4rem 1.5rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    /* About Section */
    .about-section {
        padding: 5rem 1.5rem;
    }

    .about-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .about-content {
        order: 1;
    }

    .about-image {
        order: 2;
        max-width: 80%;
        margin: 0 auto;
    }

    .about-title {
        font-size: 2.5rem;
        text-align: center;
    }

    .about-text {
        font-size: 1.25rem;
        text-align: center;
    }

    /* Reviews Section */
    .reviews-section {
        padding: 5rem 1.5rem;
    }

    .reviews-title {
        font-size: 2.5rem;
        margin-bottom: 3rem;
    }

    .reviews-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    /* Contact Section */
    .contact-section {
        padding: 5rem 1.5rem;
    }

    .contact-title {
        font-size: 2.5rem;
    }

    .contact-subtitle {
        font-size: 1.25rem;
        margin-bottom: 3rem;
    }
}

/* Desktop Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .hero-grid {
        gap: 3.5rem;
    }

    .hero-title {
        font-size: 3.25rem;
    }

    .reviews-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Large Desktop (1025px+) */
@media (min-width: 1025px) {
    .hero-grid {
        gap: 4rem;
    }

    .hero-title {
        font-size: 3.5rem;
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .reviews-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
