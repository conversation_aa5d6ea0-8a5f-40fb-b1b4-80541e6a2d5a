import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import './Hub.css';
import {
  FaHome,
  FaQuestionCircle,
  FaBook,
  FaChartLine,
  FaUser,
  FaComments,
  FaCreditCard,
  FaInfoCircle,
  FaGraduationCap,
  FaTrophy,
  FaStar,
  FaRocket
} from 'react-icons/fa';

const Hub = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.user);
  const [currentQuote, setCurrentQuote] = useState(0);

  const inspiringQuotes = [
    "Education is the most powerful weapon which you can use to change the world. - <PERSON>",
    "The future belongs to those who believe in the beauty of their dreams. - <PERSON>",
    "Success is not final, failure is not fatal: it is the courage to continue that counts. - <PERSON>",
    "Your limitation—it's only your imagination.",
    "Great things never come from comfort zones.",
    "Dream it. Wish it. Do it."
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentQuote((prev) => (prev + 1) % inspiringQuotes.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [inspiringQuotes.length]);

  const navigationItems = [
    {
      title: 'Dashboard',
      description: 'Your learning overview',
      icon: FaHome,
      path: '/user/dashboard',
      color: 'from-blue-500 to-blue-600',
      hoverColor: 'from-blue-600 to-blue-700'
    },
    {
      title: 'Take Quiz',
      description: 'Test your knowledge',
      icon: FaQuestionCircle,
      path: '/user/quiz',
      color: 'from-green-500 to-green-600',
      hoverColor: 'from-green-600 to-green-700'
    },
    {
      title: 'Study Materials',
      description: 'Books, videos & notes',
      icon: FaBook,
      path: '/user/study-materials',
      color: 'from-purple-500 to-purple-600',
      hoverColor: 'from-purple-600 to-purple-700'
    },
    {
      title: 'Reports',
      description: 'Track your progress',
      icon: FaChartLine,
      path: '/user/reports',
      color: 'from-orange-500 to-orange-600',
      hoverColor: 'from-orange-600 to-orange-700'
    },
    {
      title: 'Profile',
      description: 'Manage your account',
      icon: FaUser,
      path: '/user/profile',
      color: 'from-indigo-500 to-indigo-600',
      hoverColor: 'from-indigo-600 to-indigo-700'
    },
    {
      title: 'Forum',
      description: 'Connect with peers',
      icon: FaComments,
      path: '/user/forum',
      color: 'from-pink-500 to-pink-600',
      hoverColor: 'from-pink-600 to-pink-700'
    },
    {
      title: 'Ranking',
      description: 'See your position',
      icon: FaTrophy,
      path: '/user/ranking',
      color: 'from-yellow-500 to-yellow-600',
      hoverColor: 'from-yellow-600 to-yellow-700'
    },
    {
      title: 'Plans',
      description: 'Upgrade your learning',
      icon: FaCreditCard,
      path: '/user/plans',
      color: 'from-emerald-500 to-emerald-600',
      hoverColor: 'from-emerald-600 to-emerald-700'
    },
    {
      title: 'About Us',
      description: 'Learn about our mission',
      icon: FaInfoCircle,
      path: '/user/about',
      color: 'from-cyan-500 to-cyan-600',
      hoverColor: 'from-cyan-600 to-cyan-700'
    }
  ];



  return (
    <>
      <div className="hub-container">
        <div className="hub-welcome">
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '1rem', marginBottom: '1rem' }}>
            <FaRocket className="text-4xl text-primary animate-bounce" />
            <h1 style={{
              fontSize: 'clamp(2rem, 5vw, 3.5rem)',
              fontWeight: '700',
              background: 'linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 50%, var(--primary) 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              Welcome Back, {user?.name || 'Sawiti'}!
            </h1>
            <FaStar className="text-4xl text-warning animate-float" />
          </div>

          <p className="text-xl font-medium text-gray-600 mb-6">
            Ready to shine today? ✨
          </p>

          {/* Inspiring Quote */}
          <div className="hub-quote">
            <p className="hub-quote-text">
              "{inspiringQuotes[currentQuote]}"
            </p>
            <p className="hub-quote-author">- Brainwave Team</p>
          </div>
        </div>
      </div>

      <div className="hub-grid-container">
        {/* Navigation Grid */}
        <div className="hub-grid">
          {navigationItems.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <div
                key={item.title}
                className={`hub-card animate-fadeInUp animate-delay-${(index + 1) * 100}`}
                onClick={() => navigate(item.path)}
              >
                <div className="hub-card-icon">
                  <IconComponent />
                </div>

                <h3 className="hub-card-title">
                  {item.title}
                </h3>

                <p className="hub-card-description">
                  {item.description}
                </p>
              </div>
            );
          })}
        </div>

        {/* Bottom Decoration */}
        <div className="text-center mt-16 animate-fadeInUp animate-delay-600">
          <div className="inline-flex items-center gap-2 text-gray-500">
            <FaGraduationCap className="text-2xl animate-bounce-gentle" />
            <span className="text-lg font-medium">Your Learning Journey Continues</span>
            <FaGraduationCap className="text-2xl animate-bounce-gentle" />
          </div>
        </div>
      </div>
    </>
  );
};

export default Hub;
