import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Tb<PERSON><PERSON>, TbQuestionMark, Tb<PERSON><PERSON><PERSON>, TbTrophy, TbPlayerPlay } from 'react-icons/tb';
import { <PERSON>, Button } from './index';

const QuizCard = ({
  quiz,
  onStart,
  onView,
  showResults = false,
  userResult = null,
  compact = false,
  className = '',
  ...props
}) => {
  const getScoreColor = (percentage) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -8, scale: 1.02 }}
      transition={{ duration: 0.3 }}
      className={`quiz-card-modern ${className}`}
    >
      <Card
        interactive
        variant="default"
        className="quiz-card overflow-hidden h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300"
        {...props}
      >
        {!compact && (
          <div className="bg-gradient-to-r from-primary-500 to-blue-600 p-6 text-white">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h3 className="text-xl font-bold mb-2 line-clamp-2">{quiz.name}</h3>
                <p className="text-blue-100 text-sm line-clamp-2 opacity-90">
                  {quiz.description || 'Test your knowledge with this comprehensive quiz'}
                </p>
              </div>
              {quiz.difficulty && (
                <span className="px-3 py-1 rounded-full text-xs font-medium bg-white/20 text-white backdrop-blur-sm">
                  {quiz.difficulty}
                </span>
              )}
            </div>
          </div>
        )}

        <div className="p-6 pb-4 bg-white">
          <div className="grid grid-cols-3 gap-3 mb-6">
            <div className="bg-gray-50 rounded-lg p-3 text-center">
              <TbQuestionMark className="w-5 h-5 text-primary-600 mx-auto mb-1" />
              <div className="text-lg font-bold text-gray-900">{quiz.questions?.length || 0}</div>
              <div className="text-xs text-gray-500">Questions</div>
            </div>

            <div className="bg-gray-50 rounded-lg p-3 text-center">
              <TbClock className="w-5 h-5 text-primary-600 mx-auto mb-1" />
              <div className="text-lg font-bold text-gray-900">{quiz.duration || 30}</div>
              <div className="text-xs text-gray-500">Minutes</div>
            </div>

            <div className="bg-gray-50 rounded-lg p-3 text-center">
              <TbUsers className="w-5 h-5 text-primary-600 mx-auto mb-1" />
              <div className="text-lg font-bold text-gray-900">{quiz.attempts || 0}</div>
              <div className="text-xs text-gray-500">Attempts</div>
            </div>
          </div>

          {quiz.image && (
            <img
              src={quiz.image}
              alt="Quiz Visual"
              className="w-full h-40 object-cover rounded-xl mb-4"
            />
          )}

          {showResults && userResult && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4 mb-4"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <TbTrophy className="w-5 h-5 text-yellow-500" />
                  <span className="text-sm font-semibold text-gray-700">Your Best Score</span>
                </div>
                <div className={`text-xl font-bold ${getScoreColor(userResult.percentage)}`}>
                  {userResult.percentage}%
                </div>
              </div>
              <div className="mt-2 text-xs text-gray-600 flex items-center space-x-2">
                <span>{userResult.correctAnswers}/{userResult.totalQuestions} correct</span>
                <span>•</span>
                <span>Completed {new Date(userResult.completedAt).toLocaleDateString()}</span>
              </div>
            </motion.div>
          )}
        </div>

        <div className="px-6 pb-6 bg-gray-50 border-t border-gray-100">
          <div className="flex space-x-3 pt-4">
            <Button
              variant="primary"
              size="md"
              className="flex-1 bg-gradient-to-r from-primary-600 to-blue-600 hover:from-primary-700 hover:to-blue-700 border-0 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              onClick={onStart}
              icon={<TbPlayerPlay />}
            >
              {showResults && userResult ? 'Retake Quiz' : 'Start Quiz'}
            </Button>

            {showResults && onView && (
              <Button
                variant="secondary"
                size="md"
                className="bg-white border-2 border-primary-200 text-primary-600 hover:bg-primary-50 hover:border-primary-300 transform hover:scale-105 transition-all duration-200"
                onClick={onView}
                icon={<TbTrophy />}
              >
                View Results
              </Button>
            )}
          </div>
        </div>
      </Card>
    </motion.div>
  );
};

export default QuizCard;
