import { Form, message, Select } from "antd"; // Added Select for dropdown
import React, { useState } from "react";
import "./index.css";
import { Link, useNavigate } from "react-router-dom";
import { registerUser, sendOTP } from "../../../apicalls/users";

function Register() {
  const [verification, setVerification] = useState(false);
  const [data, setData] = useState("");
  const [otp, setOTP] = useState("");
  const [loading, setLoading] = useState(false);
  const [schoolType, setSchoolType] = useState(""); // State to store selected school type
  const navigate = useNavigate();

  const onFinish = async (values) => {
    try {
      const response = await registerUser(values);
      if (response.success) {
        message.success(response.message);
        navigate("/login");
      } else {
        message.error(response.message);
        setVerification(false);
      }
    } catch (error) {
      message.error(error.message);
      setVerification(false);
    }
    console.log(values);
  };

  const verifyUser = async (values) => {
    if (values.otp === otp) {
      onFinish(data);
    } else {
      message.error("Invalid OTP");
    }
  };

  const generateOTP = async (formData) => {
    if (!formData.name || !formData.email || !formData.password) {
      message.error("Please fill all fields!");
      return;
    }
    setLoading(true);
    try {
      const response = await sendOTP(formData);
      if (response.success) {
        message.success(response.message);
        setData(formData);
        setOTP(response.data);
        setVerification(true);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error(error.message);
    }
    setLoading(false);
  };

  const handleSchoolTypeChange = (value) => {
    setSchoolType(value); // Update the state with selected school type
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
          {verification ? (
            <div>
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="ri-shield-check-line text-2xl text-blue-600"></i>
                </div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">Verify Your Email</h1>
                <p className="text-gray-600">Enter the OTP sent to your email</p>
              </div>

              <Form layout="vertical" onFinish={verifyUser} className="space-y-6">
                <Form.Item name="otp" label="OTP Code" initialValue="">
                  <input
                    type="number"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white text-center text-lg tracking-widest"
                    placeholder="Enter 6-digit OTP"
                    maxLength="6"
                  />
                </Form.Item>

                <button
                  type="submit"
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  Verify & Complete Registration
                </button>
              </Form>
            </div>
          ) : (
            <div>
              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="ri-user-add-line text-2xl text-blue-600"></i>
                </div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">Create Account</h1>
                <p className="text-gray-600">Join thousands of students learning with Brainwave</p>
              </div>

            <Form layout="vertical" onFinish={generateOTP} className="space-y-4">
              <Form.Item name="name" label="Full Name" initialValue="" rules={[{ required: true, message: "Please enter your name!" }]}>
                <input
                  type="text"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                  placeholder="Enter your full name"
                />
              </Form.Item>

              <Form.Item name="school" label="School" initialValue="" rules={[{ required: true, message: "Please enter your school!" }]}>
                <input
                  type="text"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                  placeholder="Enter your school name"
                />
              </Form.Item>

              <Form.Item name="level" label="Education Level" initialValue="" rules={[{ required: true, message: "Please select your level!" }]}>
                <select
                  onChange={(e) => setSchoolType(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                >
                  <option value="" disabled selected>
                    Select Education Level
                  </option>
                  <option value="Primary">Primary Education</option>
                  <option value="Secondary">Secondary Education</option>
                  <option value="Advance">Advanced Level</option>
                </select>
              </Form.Item>

              <Form.Item name="class" label="Class" initialValue="" rules={[{ required: true, message: "Please select your class!" }]}>
                <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white">
                  <option value="" disabled selected>
                    Select Your Class
                  </option>
                  {schoolType === "Primary" && (
                    <>
                      <option value="1">Class 1</option>
                      <option value="2">Class 2</option>
                      <option value="3">Class 3</option>
                      <option value="4">Class 4</option>
                      <option value="5">Class 5</option>
                      <option value="6">Class 6</option>
                      <option value="7">Class 7</option>
                    </>
                  )}
                  {schoolType === "Secondary" && (
                    <>
                      <option value="Form-1">Form 1</option>
                      <option value="Form-2">Form 2</option>
                      <option value="Form-3">Form 3</option>
                      <option value="Form-4">Form 4</option>
                    </>
                  )}
                  {schoolType === "Advance" && (
                    <>
                      <option value="Form-5">Form 5</option>
                      <option value="Form-6">Form 6</option>
                    </>
                  )}
                </select>
              </Form.Item>

              <Form.Item name="email" label="Email Address" initialValue="" rules={[{ required: true, message: "Please enter your email!" }]}>
                <input
                  type="email"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                  placeholder="Enter your email address"
                />
              </Form.Item>

              <Form.Item
                name="phoneNumber"
                label="Phone Number"
                initialValue=""
                rules={[
                  {
                    required: true,
                    message: "Please enter your phone number!",
                  },
                  {
                    pattern: /^\d{10}$/,
                    message: "Phone number must be exactly 10 digits!",
                  },
                ]}
              >
                <input
                  type="tel"
                  maxLength="10"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                  placeholder="Enter 10-digit phone number"
                />
                <p className="text-sm text-gray-500 mt-1">Used for payment verification</p>
              </Form.Item>

              <Form.Item name="password" label="Password" initialValue="" rules={[{ required: true, message: "Please enter your password!" }]}>
                <input
                  type="password"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                  placeholder="Create a strong password"
                />
              </Form.Item>

              <div className="space-y-4">
                <button
                  type="submit"
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={loading}
                >
                  {loading ? 'Creating Account...' : 'Create Account'}
                </button>

                <div className="text-center">
                  <Link
                    to="/login"
                    className="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200"
                  >
                    Already have an account? Sign in
                  </Link>
                </div>
              </div>
            </Form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Register;
