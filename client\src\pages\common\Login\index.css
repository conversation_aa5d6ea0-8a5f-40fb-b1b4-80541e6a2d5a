/* Modern Login Page Styles */
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-50, #eff6ff) 0%, var(--primary-100, #dbeafe) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-4, 1rem);
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.login-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-6, 1.5rem);
    display: block;
    border-radius: var(--radius-xl, 1rem);
    box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
    transition: var(--transition-normal, 300ms cubic-bezier(0.4, 0, 0.2, 1));
}

.login-logo:hover {
    transform: scale(1.05) rotate(5deg);
    box-shadow: var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1));
}

.card {
    width: 100%;
    max-width: 400px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl, 1.5rem);
    box-shadow: var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1));
    padding: var(--space-8, 2rem);
    position: relative;
    z-index: 1;
    transition: var(--transition-normal, 300ms cubic-bezier(0.4, 0, 0.2, 1));
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.login-title {
    text-align: center;
    font-size: var(--font-size-2xl, 1.5rem);
    font-weight: 700;
    color: var(--gray-900, #111827);
    margin-bottom: var(--space-2, 0.5rem);
    background: linear-gradient(135deg, var(--primary, #007BFF) 0%, var(--primary-dark, #0056D2) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-subtitle {
    text-align: center;
    color: var(--gray-600, #4b5563);
    margin-bottom: var(--space-8, 2rem);
    font-size: var(--font-size-sm, 0.875rem);
}

.login-form .form-group {
    margin-bottom: var(--space-5, 1.25rem);
}

.login-form .form-control {
    background: rgba(255, 255, 255, 0.8);
    border: 2px solid var(--gray-200, #e5e7eb);
    transition: var(--transition-normal, 300ms cubic-bezier(0.4, 0, 0.2, 1));
}

.login-form .form-control:focus {
    background: rgba(255, 255, 255, 1);
    border-color: var(--primary, #007BFF);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.login-form .btn {
    width: 100%;
    margin-top: var(--space-4, 1rem);
    padding: var(--space-4, 1rem) var(--space-6, 1.5rem);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.login-links {
    text-align: center;
    margin-top: var(--space-6, 1.5rem);
    padding-top: var(--space-6, 1.5rem);
    border-top: 1px solid var(--gray-200, #e5e7eb);
}

.login-links a {
    color: var(--primary, #007BFF);
    font-weight: 500;
    transition: var(--transition-normal, 300ms cubic-bezier(0.4, 0, 0.2, 1));
}

.login-links a:hover {
    color: var(--primary-dark, #0056D2);
    text-decoration: underline;
}

/* Responsive Design */
@media only screen and (max-width: 640px) {
    .login-container {
        padding: var(--space-3, 0.75rem);
    }

    .card {
        padding: var(--space-6, 1.5rem);
        margin: var(--space-3, 0.75rem);
    }

    .login-logo {
        width: 60px;
        height: 60px;
    }

    .login-title {
        font-size: var(--font-size-xl, 1.25rem);
    }
}

@media only screen and (max-width: 480px) {
    .card {
        padding: var(--space-4, 1rem);
    }

    .login-form .form-control {
        padding: var(--space-3, 0.75rem);
    }

    .login-form .btn {
        padding: var(--space-3, 0.75rem) var(--space-4, 1rem);
    }
}