/* Base styles - enhanced for modern design */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap");

/* Global font family */
* {
  font-family: "Inter", "Roboto", "Nunito", system-ui, -apple-system, sans-serif !important;
  box-sizing: border-box;
}

/* Enhanced body styles */
body {
  padding: 0;
  margin: 0;
  line-height: 1.6;
  color: var(--gray-800, #1f2937);
  background-color: var(--gray-50, #f9fafb);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* Improved text rendering */
body, input, button, select, textarea {
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
  text-rendering: optimizeLegibility;
}

/* Remove default margins and paddings */
h1, h2, h3, h4, h5, h6, p, ul, ol, li {
  margin: 0;
  padding: 0;
}

/* Improved heading styles */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  color: var(--gray-900, #111827);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

/* Improved link styles */
a {
  color: var(--primary, #007BFF);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-dark, #0056D2);
  text-decoration: underline;
}

/* List styles */
ul, ol {
  list-style: none;
}

/* Image styles */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

.katex .katex-mathml {
  width: 0 !important;
}

.katex-display>.katex {
  white-space: normal !important;
}