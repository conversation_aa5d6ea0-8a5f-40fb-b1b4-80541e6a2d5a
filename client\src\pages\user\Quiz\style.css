.pass {
    background: var(--success-light) !important;
    border: 1px solid var(--success) !important;
    color: var(--success-dark) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--space-3) !important;
}

.pass-dark {
    background: var(--success) !important;
    color: white !important;
    box-shadow: var(--shadow-md) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--space-3) !important;
}

.fail {
    background: var(--danger-light) !important;
    border: 1px solid var(--danger) !important;
    color: var(--danger-dark) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--space-3) !important;
}

.fail-dark {
    background: var(--danger) !important;
    color: white !important;
    box-shadow: var(--shadow-md) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--space-3) !important;
}

.no-attempts {
    background: var(--primary-50) !important;
    border: 1px solid var(--primary) !important;
    color: var(--primary-dark) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--space-3) !important;
}

.no-attempts-dark {
    background: var(--primary) !important;
    color: white !important;
    box-shadow: var(--shadow-md) !important;
    border-radius: var(--radius-lg) !important;
    padding: var(--space-3) !important;
}

.card-design {
    border-radius: var(--radius-lg) !important;
    box-shadow: var(--shadow-md) !important;
    border: 1px solid var(--gray-200) !important;
    transition: var(--transition-normal) !important;
    background: var(--white) !important;
    padding: var(--space-4) !important;
}

.card-design:hover {
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-lg) !important;
    border-color: var(--primary) !important;
}

.box-tags {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 16px !important;
    margin: 0px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.box-tags-button {
    border-radius: 12px;
    padding: 12px 16px !important;
    margin: 0px;
    border: none;
    width: 160px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.box-tags-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.box-tags-icon {
    margin-right: 8px;
    border-radius: 12px;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* ===== ENHANCED QUIZ CARD STYLES ===== */

.quiz-card-modern {
    position: relative;
    height: 100%;
}

.quiz-card-modern .quiz-card {
    border-radius: 1rem;
    overflow: hidden;
    background: white;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.quiz-card-modern:hover .quiz-card {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    transform: translateY(-4px);
}

.quiz-card-modern .quiz-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007BFF 0%, #0056D2 100%);
    z-index: 1;
}

/* Quiz card header gradient */
.quiz-card-modern .quiz-card .bg-gradient-to-r {
    position: relative;
    overflow: hidden;
}

.quiz-card-modern .quiz-card .bg-gradient-to-r::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.quiz-card-modern:hover .quiz-card .bg-gradient-to-r::before {
    left: 100%;
}

/* Stats boxes styling */
.quiz-card-modern .bg-gray-50 {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.quiz-card-modern .bg-gray-50:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    transform: translateY(-2px);
}

/* Button enhancements */
.quiz-card-modern .btn {
    font-weight: 600;
    letter-spacing: 0.025em;
}

/* Subject badge styling */
.quiz-card-modern .bg-gradient-to-r.from-primary-100 {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

/* Result card styling */
.quiz-card-modern .bg-gradient-to-r.from-green-50 {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.1);
}