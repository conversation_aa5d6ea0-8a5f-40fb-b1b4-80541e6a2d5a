.pass {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    border: 1px solid #86efac;
    color: #15803d;
}

.pass-dark {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white !important;
    box-shadow: 0 4px 14px 0 rgba(34, 197, 94, 0.25);
}

.fail {
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
    border: 1px solid #fca5a5;
    color: #dc2626;
}

.fail-dark {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white !important;
    box-shadow: 0 4px 14px 0 rgba(239, 68, 68, 0.25);
}

.no-attempts {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border: 1px solid #93c5fd;
    color: #1d4ed8;
}

.no-attempts-dark {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white !important;
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.25);
}

.card-design {
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid #f3f4f6;
    transition: all 0.3s ease-in-out;
}

.card-design:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.box-tags {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 16px !important;
    margin: 0px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.box-tags-button {
    border-radius: 12px;
    padding: 12px 16px !important;
    margin: 0px;
    border: none;
    width: 160px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.box-tags-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.box-tags-icon {
    margin-right: 8px;
    border-radius: 12px;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}