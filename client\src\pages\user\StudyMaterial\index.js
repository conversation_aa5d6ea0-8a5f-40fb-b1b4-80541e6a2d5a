import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import "./index.css";
import { motion, AnimatePresence } from "framer-motion";
import { getStudyMaterial } from "../../../apicalls/study";
import { useDispatch, useSelector } from "react-redux";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
// import { Card, Button, Input, Loading } from "../../../components/modern";

import PDFModal from "./PDFModal";
import {
  FaPlayCircle,
  FaBook,
  FaVideo,
  FaFileAlt,
  FaGraduationCap,
  FaDownload,
  FaEye,
  FaTimes,
  FaChevronDown,
  FaSearch,
  FaExpand,
  FaCompress,
} from "react-icons/fa";
import {
  TbVideo,
  TbFileText,
  TbBook as TbBookIcon,
  TbScho<PERSON>,
  Tb<PERSON><PERSON><PERSON>,
  <PERSON>b<PERSON><PERSON><PERSON>,
  Tb<PERSON>ortAscending,
  Tb<PERSON><PERSON>,
  TbD<PERSON>load,
  Tb<PERSON><PERSON>,
  TbCalendar,
  Tb<PERSON>ser,
  TbChevronDown as TbChevronDownIcon,
  TbChevronUp,
  TbX,
  TbAlertTriangle,
  TbInfoCircle,
  TbCheck,
  TbSubtitles,
  TbBooks,
  TbCertificate
} from "react-icons/tb";
import { primarySubjects, secondarySubjects, advanceSubjects } from "../../../data/Subjects.jsx";

function StudyMaterial() {
  const { user } = useSelector((state) => state.user);
  const dispatch = useDispatch();

  // Get user level and subjects list (case-insensitive)
  const userLevel = user?.level || 'Primary';
  const userLevelLower = userLevel.toLowerCase();
  const subjectsList = userLevelLower === 'primary'
    ? primarySubjects
    : userLevelLower === 'secondary'
      ? secondarySubjects
      : advanceSubjects;

  // Debug: Log current level and subjects
  useEffect(() => {
    console.log('📚 Study Materials - User Level:', userLevel);
    console.log('📚 Study Materials - User Level (lowercase):', userLevelLower);
    console.log('📚 Study Materials - Subjects List:', subjectsList);
    console.log('📚 Study Materials - User Data:', user);
  }, [userLevel, userLevelLower, subjectsList, user]);

  // Define all possible classes for each level
  const allPossibleClasses = userLevelLower === 'primary'
    ? ['1', '2', '3', '4', '5', '6', '7']
    : userLevelLower === 'secondary'
      ? ['Form-1', 'Form-2', 'Form-3', 'Form-4']
      : ['Form-5', 'Form-6'];

  // Simplified state management - initialize with user's class if available
  const [activeTab, setActiveTab] = useState("videos");
  const [selectedClass, setSelectedClass] = useState(user?.class || user?.className || "all");
  const [selectedSubject, setSelectedSubject] = useState("all");

  // Get user's current class for highlighting
  const userCurrentClass = user?.class || user?.className;
  const [materials, setMaterials] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const [showVideoIndices, setShowVideoIndices] = useState([]);
  const [modalIsOpen, setModalIsOpen] = useState(false);
  const [documentUrl, setDocumentUrl] = useState("");
  const [availableClasses, setAvailableClasses] = useState([]);
  const [showClassSelector, setShowClassSelector] = useState(false);
  const [isVideoExpanded, setIsVideoExpanded] = useState(false);
  const [currentVideoIndex, setCurrentVideoIndex] = useState(null);
  const [videoError, setVideoError] = useState(null);
  const [selectedSubtitle, setSelectedSubtitle] = useState('off');
  const [videoRef, setVideoRef] = useState(null);


  // Unified search and sort states
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("newest");

  // Update selectedClass when user data becomes available
  useEffect(() => {
    const userClass = user?.class || user?.className;
    if (userClass && selectedClass === "all" && !availableClasses.length) {
      setSelectedClass(userClass);
    }
  }, [user, selectedClass, availableClasses.length]);

  // Reset subject selection when user level changes
  useEffect(() => {
    if (user?.level) {
      // Check if current selected subject is valid for the new level
      const isValidSubject = subjectsList.includes(selectedSubject);
      if (!isValidSubject && selectedSubject !== "all") {
        console.log('📚 Resetting subject selection due to level change');
        setSelectedSubject("all");
      }
    }
  }, [user?.level, subjectsList, selectedSubject]);

  // Set available classes based on user level (show all possible classes)
  const setAvailableClassesForLevel = useCallback(() => {
    setAvailableClasses(allPossibleClasses);
  }, [allPossibleClasses]);

  // Simplified fetch function
  const fetchMaterials = useCallback(async () => {
    if (!activeTab || selectedClass === "default") {
      return;
    }

    setIsLoading(true);
    setError(null);
    dispatch(ShowLoading());

    try {
      // Normalize className for backend - remove "Form-" prefix if present
      const normalizedClassName = selectedClass === "all" ? "all" :
        selectedClass.toString().replace("Form-", "");

      const data = {
        content: activeTab,
        className: normalizedClassName,
        subject: selectedSubject, // This can be "all" or a specific subject
      };
      if (userLevel) {
        data.level = userLevel;
      }

      const res = await getStudyMaterial(data);

      if (res.status === 200 && res.data.success) {
        const materials = res.data.data === "empty" ? [] : res.data.data;
        setMaterials(materials);
      } else {
        setMaterials([]);
        setError(`Failed to fetch ${activeTab}. Please try again.`);
      }
    } catch (error) {
      console.error("Error fetching study material:", error);
      setMaterials([]);
      setError(`Unable to load ${activeTab}. Please check your connection and try again.`);
    } finally {
      setIsLoading(false);
      dispatch(HideLoading());
    }
  }, [activeTab, selectedClass, selectedSubject, userLevel, dispatch]);

  // Set available classes when component mounts
  useEffect(() => {
    if (user && userLevel) {
      setAvailableClassesForLevel();
    }
  }, [user, userLevel, setAvailableClassesForLevel]);

  // Fetch materials when filters change or component mounts
  useEffect(() => {
    // Only fetch if we have a valid activeTab, selectedClass, and user
    if (user && userLevel && activeTab && selectedClass && selectedClass !== "default") {
      fetchMaterials();
    }
  }, [user, userLevel, activeTab, selectedClass, selectedSubject, fetchMaterials]);

  // Handler functions
  const handleTabChange = (tab) => {
    setMaterials([]);
    setActiveTab(tab);
    setSearchTerm("");
    setSortBy("newest");
  };

  const handleSubjectChange = (subject) => {
    setMaterials([]);
    setSelectedSubject(subject);
    setSearchTerm("");
  };

  const handleClassChange = (className) => {
    setMaterials([]);
    setSelectedClass(className);
    setShowClassSelector(false);
  };

  const toggleClassSelector = () => {
    setShowClassSelector(!showClassSelector);
  };

  // Unified filtering and sorting logic
  const filteredAndSortedMaterials = useMemo(() => {
    if (!materials || materials.length === 0) {
      return [];
    }

    let filtered = materials;

    // Filter by search term (title, subject, or year)
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(material =>
        material.title.toLowerCase().includes(searchLower) ||
        material.subject.toLowerCase().includes(searchLower) ||
        (material.year && material.year.toLowerCase().includes(searchLower))
      );
    }

    // Sort by year, creation date, or title
    filtered.sort((a, b) => {
      if (sortBy === "newest") {
        // For materials with year field (books, past papers)
        if (a.year && b.year) {
          return parseInt(b.year) - parseInt(a.year);
        }
        // For videos (no year field), sort by creation date or reverse order
        else if (activeTab === "videos") {
          // Since videos are fetched in reverse order from server, maintain that for "newest"
          return 0; // Keep original order (newest first from server)
        }
        // Fallback: materials with year come first
        else if (a.year && !b.year) return -1;
        else if (!a.year && b.year) return 1;
        else return 0;
      } else if (sortBy === "oldest") {
        // For materials with year field
        if (a.year && b.year) {
          return parseInt(a.year) - parseInt(b.year);
        }
        // For videos, reverse the order
        else if (activeTab === "videos") {
          return 0; // Will be reversed after sort
        }
        // Fallback: materials with year come first
        else if (a.year && !b.year) return -1;
        else if (!a.year && b.year) return 1;
        else return 0;
      } else {
        // Sort by title alphabetically
        return a.title.localeCompare(b.title);
      }
    });

    // For videos with "oldest" sort, reverse the array
    if (activeTab === "videos" && sortBy === "oldest") {
      filtered.reverse();
    }

    return filtered;
  }, [materials, searchTerm, sortBy, activeTab]);

  // Document handlers
  const handleDocumentDownload = (documentUrl) => {
    fetch(documentUrl)
      .then((response) => response.blob())
      .then((blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = documentUrl.split("/").pop();
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      })
      .catch((error) => {
        console.error("Error downloading the file:", error);
      });
  };

  const handleDocumentPreview = (documentUrl) => {
    setDocumentUrl(documentUrl);
    setModalIsOpen(true);
  };

  // Video handlers
  const handleShowVideo = async (index) => {
    const video = filteredAndSortedMaterials[index];

    setCurrentVideoIndex(index);
    setShowVideoIndices([index]);
    setIsVideoExpanded(false);
    setVideoError(null);

    // Get signed URL for S3 videos if needed
    if (video?.videoUrl && (video.videoUrl.includes('amazonaws.com') || video.videoUrl.includes('s3.'))) {
      try {
        const signedUrl = await getSignedVideoUrl(video.videoUrl);
        video.signedVideoUrl = signedUrl;
      } catch (error) {
        console.warn('Failed to get signed URL, using original URL');
        video.signedVideoUrl = video.videoUrl;
      }
    }
  };

  const handleHideVideo = () => {
    setShowVideoIndices([]);
    setCurrentVideoIndex(null);
    setIsVideoExpanded(false);
    setVideoError(null);
    setSelectedSubtitle('off');
    setVideoRef(null);
  };

  // Handle subtitle selection
  const handleSubtitleChange = (language) => {
    setSelectedSubtitle(language);

    if (videoRef) {
      const tracks = videoRef.textTracks;

      // Disable all tracks first
      for (let i = 0; i < tracks.length; i++) {
        tracks[i].mode = 'disabled';
      }

      // Enable selected track
      if (language !== 'off') {
        for (let i = 0; i < tracks.length; i++) {
          if (tracks[i].language === language) {
            tracks[i].mode = 'showing';
            break;
          }
        }
      }
    }
  };

  const handleExpandVideo = () => {
    setIsVideoExpanded(true);
  };

  const handleCollapseVideo = () => {
    setIsVideoExpanded(false);
  };









  // Note: Auto-refresh removed since videos are now uploaded synchronously

  // Get signed URL for S3 videos to ensure access
  const getSignedVideoUrl = async (videoUrl) => {
    if (!videoUrl) return videoUrl;

    // For AWS S3 URLs, get signed URL from backend
    if (videoUrl.includes('amazonaws.com') || videoUrl.includes('s3.')) {
      try {
        const response = await fetch(`http://localhost:5000/api/study/video-signed-url?videoUrl=${encodeURIComponent(videoUrl)}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.signedUrl) {
          console.log('✅ Got signed URL for S3 video');
          return data.signedUrl;
        } else {
          console.warn('⚠️ Invalid response from signed URL endpoint:', data);
          return videoUrl;
        }
      } catch (error) {
        console.warn('⚠️ Failed to get signed URL, using original:', error.message);
        return videoUrl;
      }
    }

    // For other URLs or if signed URL fails, return as-is
    return videoUrl;
  };





  // Get appropriate thumbnail URL for video
  const getThumbnailUrl = (material) => {
    // If we have a custom thumbnail, use it
    if (material.thumbnail && material.thumbnail !== "" && material.thumbnail !== "processing") {
      return material.thumbnail;
    }

    // For YouTube videos, extract video ID and use YouTube thumbnail
    if (material.videoID && !material.videoID.includes('amazonaws.com')) {
      // Extract YouTube video ID if it's a full URL
      let videoId = material.videoID;
      if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {
        const match = videoId.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
        videoId = match ? match[1] : videoId;
      }
      return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
    }

    // For uploaded videos without thumbnails, use a default placeholder
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';
  };

  // Keyboard support for video modal
  useEffect(() => {
    const handleKeyPress = (event) => {
      if (showVideoIndices.length > 0) {
        switch (event.key) {
          case 'Escape':
            handleHideVideo();
            break;
          case 'f':
          case 'F':
            if (!isVideoExpanded) {
              handleExpandVideo();
            } else {
              handleCollapseVideo();
            }
            break;
          default:
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [showVideoIndices, isVideoExpanded]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      {/* Modern Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary-600 to-blue-600 text-white"
      >
        <div className="container-modern py-12">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                <TbBooks className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-4xl font-bold mb-2">Study Materials</h1>
                <p className="text-xl text-blue-100">
                  Access comprehensive learning resources for {userLevel} education
                </p>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="bg-white/20 backdrop-blur-sm rounded-xl px-6 py-3">
                <div className="text-sm text-blue-100 mb-1">Current Level</div>
                <div className="text-lg font-bold">{userLevel?.toUpperCase()}</div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      <div className="container-modern py-8">
        {/* Modern Navigation Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <div className="card p-2">
            <div className="flex flex-wrap gap-2">
              {[
                { key: 'videos', label: 'Videos', icon: TbVideo, color: 'text-red-600' },
                { key: 'study-notes', label: 'Notes', icon: TbFileText, color: 'text-blue-600' },
                { key: 'past-papers', label: 'Past Papers', icon: TbCertificate, color: 'text-purple-600' },
                { key: 'books', label: 'Books', icon: TbBookIcon, color: 'text-green-600' }
              ].map((tab) => (
                <motion.button
                  key={tab.key}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                    activeTab === tab.key
                      ? 'bg-primary-600 text-white shadow-md'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                  onClick={() => handleTabChange(tab.key)}
                >
                  <tab.icon className={`w-5 h-5 ${activeTab === tab.key ? 'text-white' : tab.color}`} />
                  <span>{tab.label}</span>
                  {activeTab === tab.key && (
                    <motion.div
                      layoutId="activeTab"
                      className="w-2 h-2 bg-white rounded-full"
                    />
                  )}
                </motion.button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Modern Filters Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mb-8"
        >
          <div className="card p-6">
            <div className="flex flex-col lg:flex-row gap-6 items-end">
              {/* Search */}
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Search Materials
                </label>
                <input
                  placeholder={`Search ${activeTab.replace('-', ' ')}...`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="form-input"
                />
              </div>

              {/* Class Filter */}
              <div className="w-full lg:w-64">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Filter by Class
                  {userCurrentClass && (
                    <span className="ml-2 text-xs text-primary-600 font-medium">
                      (Your class: {userLevelLower === 'primary' ? `Class ${userCurrentClass}` : `Form ${userCurrentClass}`})
                    </span>
                  )}
                </label>
                <div className="relative">
                  <button
                    onClick={toggleClassSelector}
                    className="w-full input-modern flex items-center justify-between"
                  >
                    <span className="flex items-center space-x-2">
                      <TbSchool className="w-4 h-4 text-gray-400" />
                      <span>
                        {selectedClass === 'all' ? 'All Classes' :
                          userLevelLower === 'primary'
                            ? `Class ${selectedClass}`
                            : `Form ${selectedClass}`
                        }
                      </span>
                      {selectedClass === userCurrentClass && (
                        <span className="badge-primary text-xs">Current</span>
                      )}
                    </span>
                    <TbChevronDownIcon className={`w-4 h-4 text-gray-400 transition-transform ${showClassSelector ? 'rotate-180' : ''}`} />
                  </button>

                  <AnimatePresence>
                    {showClassSelector && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto"
                      >
                        <button
                          className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors ${
                            selectedClass === 'all' ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'
                          }`}
                          onClick={() => handleClassChange('all')}
                        >
                          All Classes
                        </button>
                        {availableClasses.map((className, index) => (
                          <button
                            key={index}
                            className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center justify-between ${
                              selectedClass === className ? 'bg-primary-50 text-primary-700 font-medium' : 'text-gray-700'
                            }`}
                            onClick={() => handleClassChange(className)}
                          >
                            <span>
                              {userLevelLower === 'primary' ? `Class ${className}` : `Form ${className}`}
                            </span>
                            {className === userCurrentClass && (
                              <span className="badge-success text-xs">Your Class</span>
                            )}
                          </button>
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>

              {/* Subject Filter */}
              <div className="w-full lg:w-64">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Filter by Subject
                </label>
                <select
                  value={selectedSubject}
                  onChange={(e) => handleSubjectChange(e.target.value)}
                  className="input-modern"
                >
                  <option value="all">All Subjects</option>
                  {subjectsList.map((subject, index) => (
                    <option key={index} value={subject}>
                      {subject}
                    </option>
                  ))}
                </select>
              </div>

              {/* Sort */}
              <div className="w-full lg:w-48">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sort by
                </label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="input-modern"
                >
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="title">By Title</option>
                </select>
              </div>

              {/* Clear Filters */}
              <button
                className="btn btn-secondary"
                onClick={() => {
                  setSearchTerm("");
                  setSelectedClass("all");
                  setSelectedSubject("all");
                  setSortBy("newest");
                }}
              >
                Clear Filters
              </button>
            </div>

            {/* Results Count */}
            {(searchTerm || selectedClass !== "all" || selectedSubject !== "all") && (
              <div className="mt-4 pt-4 border-t border-gray-100">
                <span className="text-sm text-gray-600">
                  Showing {filteredAndSortedMaterials.length} of {materials.length} {activeTab.replace('-', ' ')}
                </span>
              </div>
            )}
          </div>
        </motion.div>

      {/* Materials Display */}
      <div className="materials-section">
        {isLoading ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>Loading materials...</p>
          </div>
        ) : error ? (
          <div className="error-state">
            <FaTimes className="error-icon" />
            <h3>Error Loading Materials</h3>
            <p>{error}</p>
            <button
              className="retry-btn"
              onClick={() => {
                setError(null);
                fetchMaterials();
              }}
            >
              Try Again
            </button>
          </div>
        ) : filteredAndSortedMaterials.length > 0 ? (
          <div className="materials-grid">
            {filteredAndSortedMaterials.map((material, index) => (
              <div key={index} className="material-card">
                <div className="card-header">
                  <div className="material-type">
                    {activeTab === 'videos' && <FaVideo className="type-icon" />}
                    {activeTab === 'study-notes' && <FaFileAlt className="type-icon" />}
                    {activeTab === 'past-papers' && <FaFileAlt className="type-icon" />}
                    {activeTab === 'books' && <FaBook className="type-icon" />}
                    <span className="type-label">
                      {activeTab === 'study-notes' ? 'Note' :
                       activeTab === 'past-papers' ? 'Past Paper' :
                       activeTab === 'videos' ? 'Video' : 'Book'}
                    </span>
                  </div>
                  <div className="header-right">
                    {/* Class tags for videos */}
                    {activeTab === 'videos' && material.coreClass && (
                      <div className="class-tags">
                        {material.isCore ? (
                          <span className="class-tag core-class">
                            Core Class {userLevelLower === 'primary' ? material.coreClass : `Form ${material.coreClass}`}
                          </span>
                        ) : material.sharedFromClass && (
                          <span className="class-tag shared-class">
                            Shared from {userLevelLower === 'primary' ? `Class ${material.sharedFromClass}` : `Form ${material.sharedFromClass}`}
                          </span>
                        )}
                      </div>
                    )}
                    {material.year && (
                      <span className="material-year">{material.year}</span>
                    )}
                  </div>
                </div>

                {/* Video Thumbnail for videos */}
                {activeTab === 'videos' && (material.videoUrl || material.videoID) && (
                  <div className="video-thumbnail-container" onClick={() => handleShowVideo(index)}>
                    <img
                      src={getThumbnailUrl(material)}
                      alt={material.title}
                      className="video-thumbnail"
                      onError={(e) => {
                        // Fallback logic for failed thumbnails
                        if (material.videoID && !material.videoID.includes('amazonaws.com')) {
                          // For YouTube videos, try different quality thumbnails
                          let videoId = material.videoID;
                          if (videoId.includes('youtube.com') || videoId.includes('youtu.be')) {
                            const match = videoId.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
                            videoId = match ? match[1] : videoId;
                          }

                          if (!e.target.src.includes('youtube.com')) {
                            e.target.src = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
                          } else if (e.target.src.includes('maxresdefault')) {
                            e.target.src = `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;
                          } else if (e.target.src.includes('mqdefault')) {
                            e.target.src = `https://img.youtube.com/vi/${videoId}/hqdefault.jpg`;
                          } else {
                            // Final fallback to default placeholder
                            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';
                          }
                        } else {
                          // For uploaded videos without thumbnails, use default placeholder
                          e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDY0MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iMzYwIiBmaWxsPSIjZjVmNWY1Ii8+CjxwYXRoIGQ9Ik0yNTYgMTgwTDM4NCAyNDBMMjU2IDMwMFYxODBaIiBmaWxsPSIjY2NjY2NjIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOTk5OTk5IiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiPk5vIFRodW1ibmFpbDwvdGV4dD4KPHN2Zz4K';
                        }
                      }}
                    />
                    <div className="play-overlay">
                      <FaPlayCircle className="play-icon" />
                      <span className="play-text">Watch Now</span>
                    </div>
                  </div>
                )}

                <div className="card-content">
                  <h3 className="material-title">{material.title}</h3>
                  <div className="material-meta">
                    <span className="material-subject">{material.subject}</span>
                    {material.className && (
                      <span className="material-class">
                        {userLevelLower === 'primary' ? `Class ${material.className}` : `Form ${material.className}`}
                      </span>
                    )}
                  </div>
                </div>

                <div className="card-actions">
                  {activeTab === 'videos' && (material.videoUrl || material.videoID) ? (
                    <div className="video-info-text">
                      <span className="video-duration">Click thumbnail to play</span>
                    </div>
                  ) : material.documentUrl ? (
                    <>
                      <button
                        className="action-btn secondary"
                        onClick={() => handleDocumentPreview(material.documentUrl)}
                      >
                        <FaEye /> View
                      </button>
                      <button
                        className="action-btn primary"
                        onClick={() => handleDocumentDownload(material.documentUrl)}
                      >
                        <FaDownload /> Download
                      </button>
                    </>
                  ) : (
                    <span className="unavailable">Not available</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="empty-state">
            <FaGraduationCap className="empty-icon" />
            <h3>No Materials Found</h3>
            <p>No study materials are available for your current selection.</p>
            <p className="suggestion">Try selecting a different class or subject.</p>
          </div>
        )}
      </div>

      {/* Enhanced Video Display */}
      {showVideoIndices.length > 0 && currentVideoIndex !== null && (
        <div className={`video-overlay ${isVideoExpanded ? 'expanded' : ''}`} onClick={(e) => {
          if (e.target === e.currentTarget) handleHideVideo();
        }}>
          <div className={`video-modal ${isVideoExpanded ? 'expanded' : ''}`}>
            {(() => {
              const video = filteredAndSortedMaterials[currentVideoIndex];
              if (!video) return <div>Video not found</div>;

              return (
                <>
                  <div className="video-header">
                    <div className="video-info">
                      <h3>{video.title || 'Untitled Video'}</h3>
                      <div className="video-meta">
                        <span className="video-subject">{video.subject || 'Unknown Subject'}</span>
                        <span className="video-class">Class {video.className || 'N/A'}</span>
                      </div>
                    </div>
                    <div className="video-controls">
                      {!isVideoExpanded ? (
                        <button
                          className="control-btn expand-btn"
                          onClick={handleExpandVideo}
                          title="Expand to fullscreen"
                        >
                          <FaExpand />
                        </button>
                      ) : (
                        <button
                          className="control-btn collapse-btn"
                          onClick={handleCollapseVideo}
                          title="Exit fullscreen"
                        >
                          <FaCompress />
                        </button>
                      )}
                      <button
                        className="control-btn close-btn"
                        onClick={handleHideVideo}
                        title="Close video"
                      >
                        <FaTimes />
                      </button>
                    </div>
                  </div>
                  <div className="video-container">
                    {video.videoUrl ? (
                      <div style={{ padding: '15px', background: '#000', borderRadius: '8px' }}>
                          <video
                            ref={(ref) => setVideoRef(ref)}
                            controls
                            autoPlay
                            playsInline
                            preload="metadata"
                            width="100%"
                            height="400"
                            poster={getThumbnailUrl(video)}
                            style={{
                              width: '100%',
                              height: '400px',
                              backgroundColor: '#000'
                            }}
                            onError={(e) => {
                              setVideoError(`Failed to load video: ${video.title}. Please try refreshing the page.`);
                            }}
                            onCanPlay={() => {
                              setVideoError(null);
                            }}
                            onLoadedMetadata={() => {
                              // Auto-enable first subtitle if available and none selected
                              if (video.subtitles && video.subtitles.length > 0 && selectedSubtitle === 'off') {
                                const defaultSubtitle = video.subtitles.find(sub => sub.isDefault) || video.subtitles[0];
                                handleSubtitleChange(defaultSubtitle.language);
                              }
                            }}
                            crossOrigin="anonymous"
                          >
                            {/* Use signed URL if available, otherwise use original URL */}
                            <source src={video.signedVideoUrl || video.videoUrl} type="video/mp4" />

                            {/* Add subtitle tracks if available */}
                            {video.subtitles && video.subtitles.length > 0 && video.subtitles.map((subtitle, index) => (
                              <track
                                key={`${subtitle.language}-${index}`}
                                kind="subtitles"
                                src={subtitle.url}
                                srcLang={subtitle.language}
                                label={subtitle.languageName}
                                default={subtitle.isDefault || index === 0}
                              />
                            ))}

                            <p style={{color: 'white', textAlign: 'center', padding: '20px'}}>
                              Your browser does not support the video tag.
                              <br />
                              <a href={video.signedVideoUrl || video.videoUrl} target="_blank" rel="noopener noreferrer" style={{color: '#4fc3f7'}}>
                                Click here to open video in new tab
                              </a>
                            </p>
                          </video>

                          {/* Custom Subtitle Controls */}
                          {video.subtitles && video.subtitles.length > 0 && (
                            <div style={{
                              padding: '12px 15px',
                              background: 'rgba(0,0,0,0.8)',
                              borderRadius: '0 0 8px 8px',
                              borderTop: '1px solid #333'
                            }}>
                              <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '12px',
                                flexWrap: 'wrap'
                              }}>
                                <span style={{
                                  color: '#fff',
                                  fontSize: '14px',
                                  fontWeight: '500',
                                  minWidth: 'fit-content'
                                }}>
                                  📝 Choose Language:
                                </span>

                                <div style={{
                                  display: 'flex',
                                  gap: '8px',
                                  flexWrap: 'wrap',
                                  flex: 1
                                }}>
                                  {/* Off Button */}
                                  <button
                                    onClick={() => handleSubtitleChange('off')}
                                    style={{
                                      padding: '6px 12px',
                                      borderRadius: '20px',
                                      border: 'none',
                                      fontSize: '12px',
                                      fontWeight: '500',
                                      cursor: 'pointer',
                                      transition: 'all 0.2s ease',
                                      backgroundColor: selectedSubtitle === 'off' ? '#ff4757' : '#2f3542',
                                      color: '#fff'
                                    }}
                                  >
                                    OFF
                                  </button>

                                  {/* Language Buttons */}
                                  {video.subtitles.map((subtitle) => (
                                    <button
                                      key={subtitle.language}
                                      onClick={() => handleSubtitleChange(subtitle.language)}
                                      style={{
                                        padding: '6px 12px',
                                        borderRadius: '20px',
                                        border: 'none',
                                        fontSize: '12px',
                                        fontWeight: '500',
                                        cursor: 'pointer',
                                        transition: 'all 0.2s ease',
                                        backgroundColor: selectedSubtitle === subtitle.language ? '#2ed573' : '#57606f',
                                        color: '#fff',
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '4px'
                                      }}
                                    >
                                      <span>{subtitle.languageName}</span>
                                      {subtitle.isAutoGenerated && (
                                        <span style={{
                                          fontSize: '10px',
                                          opacity: 0.8,
                                          backgroundColor: 'rgba(255,255,255,0.2)',
                                          padding: '1px 4px',
                                          borderRadius: '8px'
                                        }}>
                                          AI
                                        </span>
                                      )}
                                    </button>
                                  ))}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                    ) : video.videoID ? (
                      // Fallback to YouTube embed if no videoUrl
                      <iframe
                        src={`https://www.youtube.com/embed/${video.videoID}?autoplay=1&rel=0`}
                        title={video.title}
                        frameBorder="0"
                        allowFullScreen
                        className="video-iframe"
                        onLoad={() => console.log('✅ YouTube iframe loaded')}
                      ></iframe>
                    ) : (
                      <div className="video-error">
                        <div className="error-icon">⚠️</div>
                        <h3>Video Unavailable</h3>
                        <p>{videoError || "This video cannot be played at the moment."}</p>
                        <div className="error-actions">
                          <a
                            href={video.signedVideoUrl || video.videoUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="external-link-btn"
                          >
                            📱 Open in New Tab
                          </a>
                        </div>
                      </div>
                    )}
                  </div>
                  {!isVideoExpanded && (
                    <div className="video-footer">
                      <div className="video-description">
                        <p>Watch this educational video to learn more about {video.subject}.</p>
                        {video.subtitleGenerationStatus === 'processing' && (
                          <div className="subtitle-status" style={{
                            marginTop: '10px',
                            fontSize: '0.9em',
                            color: '#2196F3',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px'
                          }}>
                            <div style={{
                              width: '16px',
                              height: '16px',
                              border: '2px solid #2196F3',
                              borderTop: '2px solid transparent',
                              borderRadius: '50%',
                              animation: 'spin 1s linear infinite'
                            }}></div>
                            🤖 Generating subtitles...
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </>
              );
            })()}
          </div>
        </div>
      )}

      {/* PDF Modal */}
      <PDFModal
        modalIsOpen={modalIsOpen}
        closeModal={() => {
          setModalIsOpen(false);
          setDocumentUrl("");
        }}
        documentUrl={documentUrl}
      />
      </div>
    </div>
  );
}

export default StudyMaterial;
