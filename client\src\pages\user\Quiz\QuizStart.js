import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import { getExamById } from '../../../apicalls/exams';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import { Card, Button, Loading } from '../../../components/modern';
import { TbClock, TbQuestionMark, TbTrophy, TbAlertTriangle, TbPlayerPlay, TbArrowLeft, TbBrain } from 'react-icons/tb';
import './responsive.css';

const QuizStart = () => {
  const [examData, setExamData] = useState(null);
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);

  useEffect(() => {
    const fetchExamData = async () => {
      try {
        dispatch(ShowLoading());
        const response = await getExamById({ examId: id });
        dispatch(HideLoading());
        
        if (response.success) {
          setExamData(response.data);
        } else {
          message.error(response.message);
          navigate('/user/quiz');
        }
      } catch (error) {
        dispatch(HideLoading());
        message.error(error.message);
        navigate('/user/quiz');
      }
    };

    if (id) {
      fetchExamData();
    }
  }, [id, dispatch, navigate]);

  const handleStartQuiz = () => {
    navigate(`/quiz/${id}/play`);
  };

  if (!examData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center">
        <Loading fullScreen text="Loading quiz details..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-4xl mx-auto px-6 py-12">
        {/* Modern Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mb-6">
            <TbBrain className="w-8 h-8 text-primary-600" />
          </div>
          <h1 className="heading-2 text-gradient mb-4">
            {examData.name}
          </h1>
          <p className="text-xl text-gray-600">
            Ready to challenge yourself? Let's test your knowledge!
          </p>
        </motion.div>

        {/* Modern Quiz Info Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="p-8 mb-8">
            <div className="grid md:grid-cols-2 gap-8">
              {/* Quiz Details */}
              <div>
                <h2 className="heading-3 mb-6 flex items-center">
                  <TbQuestionMark className="w-6 h-6 text-primary-600 mr-2" />
                  Quiz Details
                </h2>
                <div className="space-y-4">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                    className="flex items-center justify-between p-4 bg-primary-50 rounded-lg border border-primary-100"
                  >
                    <div className="flex items-center space-x-2">
                      <TbQuestionMark className="w-5 h-5 text-primary-600" />
                      <span className="font-medium text-gray-700">Questions</span>
                    </div>
                    <span className="font-bold text-primary-600">{examData.questions?.length || 0}</span>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                    className="flex items-center justify-between p-4 bg-success-50 rounded-lg border border-success-100"
                  >
                    <div className="flex items-center space-x-2">
                      <TbClock className="w-5 h-5 text-success-600" />
                      <span className="font-medium text-gray-700">Duration</span>
                    </div>
                    <span className="font-bold text-success-600">{Math.floor(examData.duration / 60)} minutes</span>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 }}
                    className="flex items-center justify-between p-4 bg-warning-50 rounded-lg border border-warning-100"
                  >
                    <div className="flex items-center space-x-2">
                      <TbTrophy className="w-5 h-5 text-warning-600" />
                      <span className="font-medium text-gray-700">Total Marks</span>
                    </div>
                    <span className="font-bold text-warning-600">{examData.totalMarks}</span>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 }}
                    className="flex items-center justify-between p-4 bg-blue-50 rounded-lg border border-blue-100"
                  >
                    <div className="flex items-center space-x-2">
                      <TbTrophy className="w-5 h-5 text-blue-600" />
                      <span className="font-medium text-gray-700">Passing Marks</span>
                    </div>
                    <span className="font-bold text-blue-600">{examData.passingMarks}</span>
                  </motion.div>
                </div>
              </div>

              {/* Instructions */}
              <div>
                <h2 className="heading-3 mb-6 flex items-center">
                  <TbAlertTriangle className="w-6 h-6 text-warning-600 mr-2" />
                  Instructions
                </h2>
                <div className="space-y-4 text-gray-700">
                  {[
                    "Read each question carefully before answering",
                    "You can navigate between questions using Previous/Next buttons",
                    "Make sure to answer all questions before submitting",
                    "Keep an eye on the timer - the quiz will auto-submit when time runs out"
                  ].map((instruction, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 + index * 0.1 }}
                      className="flex items-start space-x-3"
                    >
                      <div className="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-primary-600 font-bold text-sm">{index + 1}</span>
                      </div>
                      <p className="leading-relaxed">{instruction}</p>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Modern User Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="p-6 mb-8 bg-gradient-to-r from-primary-50 to-blue-50 border-primary-200">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                <span className="text-primary-600 font-bold text-lg">
                  {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                </span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Welcome, {user?.name || 'Student'}!</h3>
                <p className="text-gray-600">
                  Level: <span className="badge-primary">{user?.level || 'Primary'}</span> •
                  Class: <span className="badge-primary">{user?.class || 'N/A'}</span>
                </p>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Modern Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="flex flex-col sm:flex-row gap-4 justify-center"
        >
          <Button
            variant="secondary"
            size="lg"
            onClick={() => navigate('/user/quiz')}
            icon={<TbArrowLeft />}
            className="sm:w-auto w-full"
          >
            Back to Quizzes
          </Button>
          <Button
            variant="gradient"
            size="lg"
            onClick={handleStartQuiz}
            icon={<TbPlayerPlay />}
            iconPosition="right"
            className="sm:w-auto w-full"
          >
            Start Quiz
          </Button>
        </motion.div>
      </div>
    </div>
  );
};

export default QuizStart;
